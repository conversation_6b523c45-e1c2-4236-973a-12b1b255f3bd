'use client';

import React, { createContext, useContext, useEffect, useRef } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { useActivityTracker, useUserActionTracker } from '@/hooks/useActivityTracker';
import { ActivityService } from '@/lib/services/activity/activity-service';
import { SessionService } from '@/lib/services/activity/session-service';
import { GeolocationService } from '@/lib/services/activity/geolocation-service';
import { ActivityConfig } from '@/types/activity';
import { log } from '@/lib/utils/logger';

interface ActivityTrackerContextType {
  isEnabled: boolean;
  config: ActivityConfig;
  updateConfig: (newConfig: Partial<ActivityConfig>) => void;
}

const ActivityTrackerContext = createContext<ActivityTrackerContextType | undefined>(undefined);

export function useActivityTrackerContext(): ActivityTrackerContextType {
  const context = useContext(ActivityTrackerContext);
  if (context === undefined) {
    throw new Error('useActivityTrackerContext must be used within an ActivityTrackerProvider');
  }
  return context;
}

interface ActivityTrackerProviderProps {
  children: React.ReactNode;
  config?: Partial<ActivityConfig>;
}

const DEFAULT_CONFIG: ActivityConfig = {
  enabled: true,
  trackAnonymous: true,
  trackPageViews: true,
  trackClicks: true,
  trackFormSubmissions: true,
  trackPerformance: true,
  trackErrors: true,
  samplingRate: 1.0, // Track 100% of activities
  performanceSamplingRate: 0.1, // Track 10% of performance metrics
  batchSize: 10,
  batchTimeout: 5000, // 5 seconds
  excludePersonalData: true,
  hashIpAddresses: false,
  retentionDays: 90
};

export function ActivityTrackerProvider({ children, config: initialConfig = {} }: ActivityTrackerProviderProps) {
  const { user } = useAuth();
  const configRef = useRef<ActivityConfig>({ ...DEFAULT_CONFIG, ...initialConfig });
  const isInitialized = useRef(false);
  const sessionId = useRef<string | null>(null);

  // Initialize activity tracking
  useEffect(() => {
    if (isInitialized.current || !configRef.current.enabled) return;

    const initializeTracking = async () => {
      try {
        // Initialize activity service
        ActivityService.initialize();

        // Initialize session tracking
        sessionId.current = await SessionService.initialize(user?.uid);

        // Set up error tracking
        if (configRef.current.trackErrors) {
          setupErrorTracking();
        }

        // Set up performance tracking
        if (configRef.current.trackPerformance) {
          setupPerformanceTracking();
        }

        // Set up unhandled promise rejection tracking
        setupUnhandledRejectionTracking();

        isInitialized.current = true;
        
        log.info('Activity tracking initialized', 'ACTIVITY_PROVIDER', {
          userId: user?.uid,
          sessionId: sessionId.current,
          config: configRef.current
        });
      } catch (error) {
        log.error('Failed to initialize activity tracking', 'ACTIVITY_PROVIDER', { error });
      }
    };

    initializeTracking();
  }, [user?.uid]);

  // Update session when user changes
  useEffect(() => {
    if (isInitialized.current && sessionId.current && user?.uid) {
      SessionService.updateSessionUser(user.uid);
    }
  }, [user?.uid]);

  // Set up error tracking
  const setupErrorTracking = () => {
    const originalConsoleError = console.error;
    console.error = (...args: any[]) => {
      // Track console errors
      if (shouldSample(configRef.current.samplingRate)) {
        const errorMessage = args.map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
        ).join(' ');
        
        ActivityService.trackActivity('error', {
          errorMessage,
          page: window.location.pathname,
          customData: { source: 'console.error' }
        }, user?.uid);
      }
      
      originalConsoleError.apply(console, args);
    };

    // Track unhandled errors
    window.addEventListener('error', (event) => {
      if (shouldSample(configRef.current.samplingRate)) {
        ActivityService.trackActivity('error', {
          errorMessage: event.message,
          errorStack: event.error?.stack,
          page: window.location.pathname,
          customData: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            source: 'window.error'
          }
        }, user?.uid);
      }
    });
  };

  // Set up performance tracking
  const setupPerformanceTracking = () => {
    // Track Core Web Vitals
    if ('web-vital' in window) {
      // This would integrate with web-vitals library if available
      // For now, we'll track basic performance metrics
    }

    // Track resource loading performance
    const observer = new PerformanceObserver((list) => {
      if (!shouldSample(configRef.current.performanceSamplingRate)) return;

      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          ActivityService.trackActivity('performance', {
            customData: {
              type: 'navigation',
              loadTime: navEntry.loadEventEnd - navEntry.fetchStart,
              domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.fetchStart,
              firstPaint: navEntry.responseEnd - navEntry.fetchStart,
              transferSize: navEntry.transferSize,
              encodedBodySize: navEntry.encodedBodySize
            },
            page: window.location.pathname
          }, user?.uid);
        } else if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming;
          // Only track slow resources
          if (resourceEntry.duration > 1000) {
            ActivityService.trackActivity('performance', {
              customData: {
                type: 'resource',
                name: resourceEntry.name,
                duration: resourceEntry.duration,
                transferSize: resourceEntry.transferSize,
                encodedBodySize: resourceEntry.encodedBodySize
              },
              page: window.location.pathname
            }, user?.uid);
          }
        }
      }
    });

    observer.observe({ entryTypes: ['navigation', 'resource'] });

    // Track long tasks
    if ('PerformanceObserver' in window) {
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          if (!shouldSample(configRef.current.performanceSamplingRate)) return;

          for (const entry of list.getEntries()) {
            ActivityService.trackActivity('performance', {
              customData: {
                type: 'longtask',
                duration: entry.duration,
                startTime: entry.startTime
              },
              page: window.location.pathname
            }, user?.uid);
          }
        });

        longTaskObserver.observe({ entryTypes: ['longtask'] });
      } catch (error) {
        // Long task observer not supported
      }
    }
  };

  // Set up unhandled promise rejection tracking
  const setupUnhandledRejectionTracking = () => {
    window.addEventListener('unhandledrejection', (event) => {
      if (shouldSample(configRef.current.samplingRate)) {
        ActivityService.trackActivity('error', {
          errorMessage: event.reason?.message || String(event.reason),
          errorStack: event.reason?.stack,
          page: window.location.pathname,
          customData: {
            source: 'unhandledrejection',
            type: typeof event.reason
          }
        }, user?.uid);
      }
    });
  };

  // Sampling utility
  const shouldSample = (rate: number): boolean => {
    return Math.random() < rate;
  };

  // Update configuration
  const updateConfig = (newConfig: Partial<ActivityConfig>) => {
    configRef.current = { ...configRef.current, ...newConfig };
    log.info('Activity tracking config updated', 'ACTIVITY_PROVIDER', { 
      newConfig: configRef.current 
    });
  };

  const contextValue: ActivityTrackerContextType = {
    isEnabled: configRef.current.enabled,
    config: configRef.current,
    updateConfig
  };

  return (
    <ActivityTrackerContext.Provider value={contextValue}>
      {children}
    </ActivityTrackerContext.Provider>
  );
}

// Higher-order component for pages that need activity tracking
export function withActivityTracking<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    trackPageView?: boolean;
    trackInteractions?: boolean;
    customConfig?: Partial<ActivityConfig>;
  } = {}
) {
  const {
    trackPageView = true,
    trackInteractions = true,
    customConfig = {}
  } = options;

  return function ActivityTrackedPage(props: P) {
    const { user } = useAuth();
    const tracker = useActivityTracker({
      trackPageViews: trackPageView,
      trackClicks: trackInteractions,
      trackFormSubmissions: trackInteractions,
      enabled: true
    });

    // Track page view on mount
    useEffect(() => {
      if (trackPageView) {
        tracker.trackPageView();
      }
    }, [tracker, trackPageView]);

    return (
      <ActivityTrackerProvider config={customConfig}>
        <Component {...props} />
      </ActivityTrackerProvider>
    );
  };
}

// Hook for manual activity tracking
export function useManualActivityTracker() {
  const { user } = useAuth();
  const { config } = useActivityTrackerContext();
  const userActionTracker = useUserActionTracker();

  const trackCustomEvent = async (
    eventName: string,
    eventData: Record<string, any> = {},
    category?: string
  ) => {
    if (!config.enabled) return;

    await ActivityService.trackActivity('system_event', {
      customData: {
        eventName,
        eventData,
        category
      },
      page: window.location.pathname
    }, user?.uid);
  };

  const trackBusinessEvent = async (
    eventType: 'checklist_action' | 'equipment_action' | 'admin_action' | 'user_action',
    action: string,
    entityId?: string,
    additionalData?: Record<string, any>
  ) => {
    if (!config.enabled) return;

    const activityType = {
      checklist_action: 'checklist_create',
      equipment_action: 'equipment_create',
      admin_action: 'user_create',
      user_action: 'system_event'
    }[eventType] as any;

    await ActivityService.trackActivity(activityType, {
      customData: {
        action,
        entityId,
        ...additionalData
      },
      page: window.location.pathname
    }, user?.uid);
  };

  return {
    trackCustomEvent,
    trackBusinessEvent,
    ...userActionTracker
  };
}
