'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  Activity, 
  Clock, 
  Globe,
  Smartphone,
  Monitor,
  Tablet,
  Download,
  Filter,
  Calendar,
  BarChart3
} from 'lucide-react';
import { 
  ActivityAnalytics, 
  ActivityType, 
  DeviceType,
  ActivityFilters 
} from '@/types/activity';
import { ActivityService } from '@/lib/services/activity/activity-service';
import { SessionService, SessionAnalytics } from '@/lib/services/activity/session-service';
// import { useActivityTracker } from '@/hooks/useActivityTracker';

interface ActivityAnalyticsProps {
  className?: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export function ActivityAnalyticsComponent({ className }: ActivityAnalyticsProps) {
  const [analytics, setAnalytics] = useState<ActivityAnalytics | null>(null);
  const [sessionAnalytics, setSessionAnalytics] = useState<SessionAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');
  const [filters, setFilters] = useState<ActivityFilters>({});
  const [activeTab, setActiveTab] = useState('overview');

  // const { trackButtonClick } = useActivityTracker();

  // Load analytics data
  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      const now = new Date();
      const start = new Date();
      
      switch (timeRange) {
        case '1h':
          start.setHours(now.getHours() - 1);
          break;
        case '24h':
          start.setDate(now.getDate() - 1);
          break;
        case '7d':
          start.setDate(now.getDate() - 7);
          break;
        case '30d':
          start.setDate(now.getDate() - 30);
          break;
      }

      const [activityData, sessionData] = await Promise.all([
        ActivityService.getActivityAnalytics(filters, { start, end: now }),
        SessionService.getSessionAnalytics({ start, end: now }, filters.userId)
      ]);

      setAnalytics(activityData);
      setSessionAnalytics(sessionData);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnalytics();
  }, [timeRange, filters]);

  // Memoized chart data
  const chartData = useMemo(() => {
    if (!analytics) return null;

    // Activity type breakdown for pie chart
    const activityTypeData = analytics.typeBreakdown.map(item => ({
      name: item.type.replace('_', ' ').toUpperCase(),
      value: item.count,
      percentage: item.percentage
    }));

    // Device breakdown for pie chart
    const deviceData = analytics.deviceBreakdown.map(item => ({
      name: item.device.charAt(0).toUpperCase() + item.device.slice(1),
      value: item.count,
      percentage: item.percentage
    }));

    // Geographic breakdown for bar chart
    const geoData = analytics.geographicBreakdown.slice(0, 10).map(item => ({
      country: item.country,
      visitors: item.count,
      percentage: item.percentage
    }));

    // Page breakdown for bar chart
    const pageData = analytics.pageBreakdown.slice(0, 10).map(item => ({
      page: item.page.length > 20 ? item.page.substring(0, 20) + '...' : item.page,
      views: item.views,
      users: item.uniqueUsers,
      avgTime: Math.round(item.averageTime / 1000) // Convert to seconds
    }));

    // Activity timeline for line chart
    const timelineData = analytics.activityTimeline.map(item => ({
      time: new Date(item.timestamp).toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      activities: item.count
    }));

    return {
      activityTypeData,
      deviceData,
      geoData,
      pageData,
      timelineData
    };
  }, [analytics]);

  const handleTimeRangeChange = async (newRange: typeof timeRange) => {
    // await trackButtonClick('analytics-time-range', `Change Time Range to ${newRange}`);
    setTimeRange(newRange);
  };

  const handleExport = async () => {
    // await trackButtonClick('analytics-export', 'Export Analytics Data');
    
    if (!analytics || !sessionAnalytics) return;

    const exportData = {
      timeRange,
      filters,
      generatedAt: new Date().toISOString(),
      analytics,
      sessionAnalytics
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `activity-analytics-${timeRange}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="h-6 w-6" />
            Activity Analytics
          </h2>
          <p className="text-muted-foreground">Detailed analysis of user activity patterns</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Last Hour</SelectItem>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {analytics && sessionAnalytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Activities</p>
                  <p className="text-2xl font-bold">{analytics.totalActivities.toLocaleString()}</p>
                </div>
                <Activity className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Unique Users</p>
                  <p className="text-2xl font-bold">{analytics.uniqueUsers.toLocaleString()}</p>
                </div>
                <Users className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg Session</p>
                  <p className="text-2xl font-bold">
                    {Math.round(sessionAnalytics.averageDuration / 60000)}m
                  </p>
                </div>
                <Clock className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Bounce Rate</p>
                  <p className="text-2xl font-bold">{Math.round(sessionAnalytics.bounceRate)}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="devices">Devices</TabsTrigger>
          <TabsTrigger value="geography">Geography</TabsTrigger>
          <TabsTrigger value="pages">Pages</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Activity Timeline */}
            <Card>
              <CardHeader>
                <CardTitle>Activity Timeline</CardTitle>
                <CardDescription>Activity volume over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={chartData?.timelineData || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="activities" 
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Activity Types */}
            <Card>
              <CardHeader>
                <CardTitle>Activity Types</CardTitle>
                <CardDescription>Distribution by activity type</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={chartData?.activityTypeData || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name} (${percentage}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {chartData?.activityTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="devices" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Device Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Device Types</CardTitle>
                <CardDescription>User distribution by device</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={chartData?.deviceData || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name} (${percentage}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {chartData?.deviceData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Device Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Device Statistics</CardTitle>
                <CardDescription>Detailed device breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {chartData?.deviceData.map((device, index) => (
                    <div key={device.name} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {device.name === 'Desktop' && <Monitor className="h-4 w-4" />}
                        {device.name === 'Mobile' && <Smartphone className="h-4 w-4" />}
                        {device.name === 'Tablet' && <Tablet className="h-4 w-4" />}
                        <span className="font-medium">{device.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{device.value}</Badge>
                        <span className="text-sm text-muted-foreground">
                          {device.percentage}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="geography" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Geographic Distribution</CardTitle>
              <CardDescription>Visitors by country</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={chartData?.geoData || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="country" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="visitors" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Page Performance</CardTitle>
              <CardDescription>Most visited pages and engagement metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={chartData?.pageData || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="page" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="views" fill="#8884d8" name="Page Views" />
                  <Bar dataKey="users" fill="#82ca9d" name="Unique Users" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
