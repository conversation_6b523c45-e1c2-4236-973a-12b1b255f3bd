'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Users, 
  Eye, 
  Globe, 
  Smartphone, 
  Monitor, 
  Tablet,
  TrendingUp,
  Clock,
  MapPin,
  RefreshCw,
  Filter,
  Download
} from 'lucide-react';
import { 
  VisitorStats, 
  UserActivity, 
  ActivityType, 
  DeviceType,
  ActivityAnalytics 
} from '@/types/activity';
import { ActivityService } from '@/lib/services/activity/activity-service';
// import { useActivityTracker } from '@/hooks/useActivityTracker';

interface ActivityDashboardProps {
  className?: string;
}

export function ActivityDashboard({ className }: ActivityDashboardProps) {
  const [visitorStats, setVisitorStats] = useState<VisitorStats | null>(null);
  const [recentActivities, setRecentActivities] = useState<UserActivity[]>([]);
  const [analytics, setAnalytics] = useState<ActivityAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');

  // const { trackButtonClick } = useActivityTracker();

  // Real-time visitor stats subscription
  useEffect(() => {
    const unsubscribe = ActivityService.onVisitorStatsChange((stats) => {
      setVisitorStats(stats);
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  // Auto-refresh analytics data
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadAnalytics();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, timeRange]);

  // Load analytics data
  const loadAnalytics = async () => {
    try {
      const now = new Date();
      const start = new Date();
      
      switch (timeRange) {
        case '1h':
          start.setHours(now.getHours() - 1);
          break;
        case '24h':
          start.setDate(now.getDate() - 1);
          break;
        case '7d':
          start.setDate(now.getDate() - 7);
          break;
        case '30d':
          start.setDate(now.getDate() - 30);
          break;
      }

      const analyticsData = await ActivityService.getActivityAnalytics({}, { start, end: now });
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    }
  };

  // Initial load
  useEffect(() => {
    loadAnalytics();
  }, [timeRange]);

  // Memoized stats cards
  const statsCards = useMemo(() => {
    if (!visitorStats) return [];

    return [
      {
        title: 'Live Visitors',
        value: visitorStats.liveVisitors,
        icon: Eye,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        description: 'Currently active'
      },
      {
        title: 'Total Visitors',
        value: visitorStats.totalVisitors,
        icon: Users,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        description: 'All time'
      },
      {
        title: 'Page Views',
        value: visitorStats.totalPageViews,
        icon: Activity,
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        description: 'Total views'
      },
      {
        title: 'Sessions',
        value: visitorStats.totalSessions,
        icon: Clock,
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        description: 'Active sessions'
      }
    ];
  }, [visitorStats]);

  // Device breakdown
  const deviceStats = useMemo(() => {
    if (!visitorStats) return [];

    const total = visitorStats.deviceBreakdown.desktop + 
                  visitorStats.deviceBreakdown.mobile + 
                  visitorStats.deviceBreakdown.tablet;

    if (total === 0) return [];

    return [
      {
        type: 'Desktop',
        count: visitorStats.deviceBreakdown.desktop,
        percentage: Math.round((visitorStats.deviceBreakdown.desktop / total) * 100),
        icon: Monitor,
        color: 'bg-blue-500'
      },
      {
        type: 'Mobile',
        count: visitorStats.deviceBreakdown.mobile,
        percentage: Math.round((visitorStats.deviceBreakdown.mobile / total) * 100),
        icon: Smartphone,
        color: 'bg-green-500'
      },
      {
        type: 'Tablet',
        count: visitorStats.deviceBreakdown.tablet,
        percentage: Math.round((visitorStats.deviceBreakdown.tablet / total) * 100),
        icon: Tablet,
        color: 'bg-purple-500'
      }
    ];
  }, [visitorStats]);

  const handleRefresh = async () => {
    // await trackButtonClick('activity-refresh', 'Refresh Activity Data');
    setLoading(true);
    await loadAnalytics();
    setLoading(false);
  };

  const handleTimeRangeChange = async (newRange: typeof timeRange) => {
    // await trackButtonClick('activity-time-range', `Change Time Range to ${newRange}`);
    setTimeRange(newRange);
  };

  const handleAutoRefreshToggle = async () => {
    // await trackButtonClick('activity-auto-refresh', `Toggle Auto Refresh: ${!autoRefresh}`);
    setAutoRefresh(!autoRefresh);
  };

  if (loading && !visitorStats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Activity className="h-6 w-6" />
            Activity Dashboard
          </h2>
          <p className="text-muted-foreground">Real-time user activity monitoring</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleAutoRefreshToggle}
            className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh
          </Button>
          
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold">{stat.value.toLocaleString()}</p>
                    <p className="text-xs text-muted-foreground">{stat.description}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Main Dashboard */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="devices">Devices</TabsTrigger>
          <TabsTrigger value="geography">Geography</TabsTrigger>
          <TabsTrigger value="activity">Activity Feed</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* User Type Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>User Types</CardTitle>
                <CardDescription>Authenticated vs Anonymous users</CardDescription>
              </CardHeader>
              <CardContent>
                {visitorStats && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Authenticated</span>
                      <Badge variant="secondary">
                        {visitorStats.authenticatedUsers}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Anonymous</span>
                      <Badge variant="outline">
                        {visitorStats.anonymousUsers}
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Top Pages */}
            <Card>
              <CardHeader>
                <CardTitle>Top Pages</CardTitle>
                <CardDescription>Most visited pages</CardDescription>
              </CardHeader>
              <CardContent>
                {visitorStats?.topPages && visitorStats.topPages.length > 0 ? (
                  <div className="space-y-2">
                    {visitorStats.topPages.slice(0, 5).map((page, index) => (
                      <div key={page.page} className="flex items-center justify-between">
                        <span className="text-sm truncate">{page.page}</span>
                        <Badge variant="secondary">{page.views}</Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No page data available</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="devices" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Device Breakdown</CardTitle>
              <CardDescription>Visitor distribution by device type</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {deviceStats.map((device) => (
                  <div key={device.type} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <device.icon className="h-4 w-4" />
                        <span className="text-sm font-medium">{device.type}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {device.count} ({device.percentage}%)
                        </span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${device.color}`}
                        style={{ width: `${device.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="geography" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Geographic Distribution</CardTitle>
              <CardDescription>Visitors by country</CardDescription>
            </CardHeader>
            <CardContent>
              {visitorStats?.topCountries && visitorStats.topCountries.length > 0 ? (
                <div className="space-y-2">
                  {visitorStats.topCountries.slice(0, 10).map((country, index) => (
                    <div key={country.country} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{country.country}</span>
                      </div>
                      <Badge variant="secondary">{country.count}</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No geographic data available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest user interactions</CardDescription>
            </CardHeader>
            <CardContent>
              {recentActivities.length > 0 ? (
                <div className="space-y-3">
                  {recentActivities.slice(0, 10).map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Activity className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">{activity.type}</p>
                          <p className="text-xs text-muted-foreground">
                            {activity.metadata.page || 'Unknown page'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-xs text-muted-foreground">
                          {new Date(typeof activity.timestamp === 'string' ? activity.timestamp : activity.timestamp.toDate()).toLocaleTimeString()}
                        </p>
                        <Badge variant="outline" className="text-xs">
                          {activity.device.type}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No recent activity</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
