'use client';

import { useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { ActivityService } from '@/lib/services/activity/activity-service';
import { ActivityType, ActivityMetadata } from '@/types/activity';
import { useRouter } from 'next/navigation';

interface UseActivityTrackerOptions {
  trackPageViews?: boolean;
  trackClicks?: boolean;
  trackFormSubmissions?: boolean;
  trackFeatureToggles?: boolean;
  trackPerformance?: boolean;
  enabled?: boolean;
}

interface ActivityTracker {
  trackActivity: (type: ActivityType, metadata?: ActivityMetadata) => Promise<void>;
  trackPageView: (page?: string, path?: string) => Promise<void>;
  trackButtonClick: (elementId: string, elementText: string, customData?: Record<string, any>) => Promise<void>;
  trackFeatureToggle: (featureName: string, featureState: boolean) => Promise<void>;
  trackFormSubmit: (formId: string, formData: Record<string, any>) => Promise<void>;
  trackError: (error: Error, context?: string) => Promise<void>;
  trackPerformance: (metric: string, value: number, context?: string) => Promise<void>;
}

export function useActivityTracker(options: UseActivityTrackerOptions = {}): ActivityTracker {
  const {
    trackPageViews = true,
    trackClicks = true,
    trackFormSubmissions = true,
    trackFeatureToggles = true,
    trackPerformance = true,
    enabled = true
  } = options;

  const { user } = useAuth();
  const router = useRouter();
  const isInitialized = useRef(false);
  const currentPage = useRef<string>('');
  const pageStartTime = useRef<number>(Date.now());

  // Initialize activity tracking
  useEffect(() => {
    if (!enabled || isInitialized.current) return;

    ActivityService.initialize();
    isInitialized.current = true;

    // Track initial page view
    if (trackPageViews) {
      const page = window.location.pathname;
      const path = window.location.href;
      currentPage.current = page;
      pageStartTime.current = Date.now();
      
      ActivityService.trackPageView(page, path, user?.uid);
    }
  }, [enabled, trackPageViews, user?.uid]);

  // Track page changes
  useEffect(() => {
    if (!enabled || !trackPageViews) return;

    const handleRouteChange = (url: string) => {
      // Track page leave for previous page
      if (currentPage.current) {
        const timeOnPage = Date.now() - pageStartTime.current;
        ActivityService.trackActivity(ActivityType.PAGE_LEAVE, {
          page: currentPage.current,
          timeOnPage
        }, user?.uid);
      }

      // Track new page view
      currentPage.current = url;
      pageStartTime.current = Date.now();
      ActivityService.trackPageView(url, window.location.href, user?.uid);
    };

    // Listen for route changes (Next.js specific)
    const originalPush = router.push;
    const originalReplace = router.replace;

    router.push = (...args: any[]) => {
      const result = originalPush.apply(router, args);
      handleRouteChange(args[0]);
      return result;
    };

    router.replace = (...args: any[]) => {
      const result = originalReplace.apply(router, args);
      handleRouteChange(args[0]);
      return result;
    };

    return () => {
      router.push = originalPush;
      router.replace = originalReplace;
    };
  }, [enabled, trackPageViews, router, user?.uid]);

  // Set up automatic click tracking
  useEffect(() => {
    if (!enabled || !trackClicks) return;

    const handleClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target) return;

      // Track button clicks
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        const button = target.tagName === 'BUTTON' ? target : target.closest('button')!;
        const elementId = button.id || button.className || 'unknown';
        const elementText = button.textContent?.trim() || button.getAttribute('aria-label') || 'unknown';
        
        ActivityService.trackButtonClick(elementId, elementText, user?.uid, {
          tagName: button.tagName,
          className: button.className,
          type: button.getAttribute('type') || 'button'
        });
      }

      // Track link clicks
      if (target.tagName === 'A' || target.closest('a')) {
        const link = target.tagName === 'A' ? target : target.closest('a')!;
        const href = link.getAttribute('href') || '';
        const linkText = link.textContent?.trim() || 'unknown';
        
        ActivityService.trackActivity(ActivityType.BUTTON_CLICK, {
          elementId: link.id || link.className || 'unknown',
          elementText: linkText,
          elementType: 'link',
          page: window.location.pathname,
          customData: {
            href,
            target: link.getAttribute('target'),
            isExternal: href.startsWith('http') && !href.includes(window.location.hostname)
          }
        }, user?.uid);
      }
    };

    document.addEventListener('click', handleClick, true);
    return () => document.removeEventListener('click', handleClick, true);
  }, [enabled, trackClicks, user?.uid]);

  // Set up form submission tracking
  useEffect(() => {
    if (!enabled || !trackFormSubmissions) return;

    const handleFormSubmit = (event: SubmitEvent) => {
      const form = event.target as HTMLFormElement;
      if (!form) return;

      const formId = form.id || form.className || 'unknown';
      const formData = new FormData(form);
      const formDataObj: Record<string, any> = {};

      // Convert FormData to object (excluding sensitive fields)
      for (const [key, value] of formData.entries()) {
        if (!key.toLowerCase().includes('password') && 
            !key.toLowerCase().includes('token') &&
            !key.toLowerCase().includes('secret')) {
          formDataObj[key] = value;
        }
      }

      ActivityService.trackFormSubmit(formId, formDataObj, user?.uid);
    };

    document.addEventListener('submit', handleFormSubmit, true);
    return () => document.removeEventListener('submit', handleFormSubmit, true);
  }, [enabled, trackFormSubmissions, user?.uid]);

  // Set up performance tracking
  useEffect(() => {
    if (!enabled || !trackPerformance) return;

    // Track page load performance
    const trackPageLoadPerformance = () => {
      if (typeof window !== 'undefined' && 'performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          const loadTime = navigation.loadEventEnd - navigation.fetchStart;
          const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
          
          ActivityService.trackActivity(ActivityType.PERFORMANCE, {
            customData: {
              loadTime,
              domContentLoaded,
              transferSize: navigation.transferSize,
              encodedBodySize: navigation.encodedBodySize,
              decodedBodySize: navigation.decodedBodySize
            },
            page: window.location.pathname
          }, user?.uid);
        }
      }
    };

    // Track when page is fully loaded
    if (document.readyState === 'complete') {
      trackPageLoadPerformance();
    } else {
      window.addEventListener('load', trackPageLoadPerformance);
    }

    return () => window.removeEventListener('load', trackPageLoadPerformance);
  }, [enabled, trackPerformance, user?.uid]);

  // Tracker methods
  const trackActivity = useCallback(async (type: ActivityType, metadata: ActivityMetadata = {}) => {
    if (!enabled) return;
    await ActivityService.trackActivity(type, metadata, user?.uid);
  }, [enabled, user?.uid]);

  const trackPageView = useCallback(async (page?: string, path?: string) => {
    if (!enabled) return;
    const currentPage = page || window.location.pathname;
    const currentPath = path || window.location.href;
    await ActivityService.trackPageView(currentPage, currentPath, user?.uid);
  }, [enabled, user?.uid]);

  const trackButtonClick = useCallback(async (
    elementId: string, 
    elementText: string, 
    customData: Record<string, any> = {}
  ) => {
    if (!enabled) return;
    await ActivityService.trackButtonClick(elementId, elementText, user?.uid, customData);
  }, [enabled, user?.uid]);

  const trackFeatureToggle = useCallback(async (featureName: string, featureState: boolean) => {
    if (!enabled) return;
    await ActivityService.trackFeatureToggle(featureName, featureState, user?.uid);
  }, [enabled, user?.uid]);

  const trackFormSubmit = useCallback(async (formId: string, formData: Record<string, any>) => {
    if (!enabled) return;
    await ActivityService.trackFormSubmit(formId, formData, user?.uid);
  }, [enabled, user?.uid]);

  const trackError = useCallback(async (error: Error, context?: string) => {
    if (!enabled) return;
    await ActivityService.trackActivity(ActivityType.ERROR, {
      errorMessage: error.message,
      errorStack: error.stack,
      page: window.location.pathname,
      customData: { context }
    }, user?.uid);
  }, [enabled, user?.uid]);

  const trackPerformance = useCallback(async (metric: string, value: number, context?: string) => {
    if (!enabled) return;
    await ActivityService.trackActivity(ActivityType.PERFORMANCE, {
      customData: { metric, value, context },
      page: window.location.pathname
    }, user?.uid);
  }, [enabled, user?.uid]);

  return {
    trackActivity,
    trackPageView,
    trackButtonClick,
    trackFeatureToggle,
    trackFormSubmit,
    trackError,
    trackPerformance
  };
}

// Higher-order component for automatic activity tracking
export function withActivityTracking<P extends object>(
  Component: React.ComponentType<P>,
  options: UseActivityTrackerOptions = {}
) {
  return function ActivityTrackedComponent(props: P) {
    const tracker = useActivityTracker(options);
    
    return <Component {...props} activityTracker={tracker} />;
  };
}

// Hook for tracking specific user actions
export function useUserActionTracker() {
  const { user } = useAuth();

  const trackLogin = useCallback(async (method: string = 'email') => {
    if (user?.uid) {
      await ActivityService.trackLogin(user.uid, method);
    }
  }, [user?.uid]);

  const trackLogout = useCallback(async () => {
    if (user?.uid) {
      await ActivityService.trackLogout(user.uid);
    }
  }, [user?.uid]);

  const trackChecklistAction = useCallback(async (action: 'create' | 'update' | 'delete' | 'export', checklistId?: string) => {
    const activityType = {
      create: ActivityType.CHECKLIST_CREATE,
      update: ActivityType.CHECKLIST_UPDATE,
      delete: ActivityType.CHECKLIST_DELETE,
      export: ActivityType.CHECKLIST_EXPORT
    }[action];

    await ActivityService.trackActivity(activityType, {
      customData: { checklistId },
      page: window.location.pathname
    }, user?.uid);
  }, [user?.uid]);

  const trackEquipmentAction = useCallback(async (action: 'create' | 'update' | 'delete', equipmentId?: string) => {
    const activityType = {
      create: ActivityType.EQUIPMENT_CREATE,
      update: ActivityType.EQUIPMENT_UPDATE,
      delete: ActivityType.EQUIPMENT_DELETE
    }[action];

    await ActivityService.trackActivity(activityType, {
      customData: { equipmentId },
      page: window.location.pathname
    }, user?.uid);
  }, [user?.uid]);

  const trackAdminAction = useCallback(async (action: 'user_create' | 'user_update' | 'user_delete' | 'role_change', targetUserId?: string, additionalData?: Record<string, any>) => {
    const activityType = {
      user_create: ActivityType.USER_CREATE,
      user_update: ActivityType.USER_UPDATE,
      user_delete: ActivityType.USER_DELETE,
      role_change: ActivityType.ROLE_CHANGE
    }[action];

    await ActivityService.trackActivity(activityType, {
      customData: { targetUserId, ...additionalData },
      page: window.location.pathname
    }, user?.uid);
  }, [user?.uid]);

  return {
    trackLogin,
    trackLogout,
    trackChecklistAction,
    trackEquipmentAction,
    trackAdminAction
  };
}
