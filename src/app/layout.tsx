import type { <PERSON>ada<PERSON>, Viewport } from "next";
import "./globals.css";
import { ThemeProvider } from "@/components/common/theme-provider";
import Navigation from "@/components/navigation";
import { Footer } from "@/components/common/footer";
import { StorageAlert } from "@/components/storage/storage-alert";
import { ConditionalAuthWrapper } from "@/components/conditional-auth-wrapper";
import { MaintenanceWrapper } from "@/components/system/maintenance-wrapper";
// import { ActivityTrackerProvider } from "@/components/activity/activity-tracker-provider";
import { Toaster } from "sonner";

export const metadata: Metadata = {
  title: "Auburn Engineering | Leading ACMV & Engineering Services in Qatar",
  description: "Auburn Engineering - Trusted leader in Mechanical Ventilation Systems, Generator Services, and Engineering Solutions in Qatar for over 15 years.",
  manifest: "/manifest.json",
  icons: {
    icon: [
      {
        url: "/favicon.ico",
        type: "image/x-icon",
      },
      {
        url: "/icons/favicon.ico",
        type: "image/x-icon",
      },
      {
        url: "/icons/icon-16x16.png",
        type: "image/png",
        sizes: "16x16",
      },
      {
        url: "/icons/icon-32x32.png",
        type: "image/png",
        sizes: "32x32",
      },
      {
        url: "/icons/icon-96x96.png",
        type: "image/png",
        sizes: "96x96",
      },
      {
        url: "/icons/icon-192x192.png",
        type: "image/png",
        sizes: "192x192",
      },
      {
        url: "/icon-192x192.png",
        type: "image/png",
        sizes: "192x192",
      },
      {
        url: "/icons/icon.png",
        type: "image/png",
        sizes: "512x512",
      },
    ],
    apple: [
      {
        url: "/icons/icon-apple.png",
        sizes: "512x512",
        type: "image/png",
      },
    ],
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: "#dc2626",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="font-inter antialiased">
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <ConditionalAuthWrapper>
            {/* <ActivityTrackerProvider> */}
              <MaintenanceWrapper>
                <div className="min-h-screen relative overflow-hidden flex flex-col">
                  <Navigation />
                  <main className="flex-1 pt-16 sm:pt-20 lg:pt-24">
                    <div className="relative">
                      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] pointer-events-none" />
                      <div className="relative z-10">
                        {children}
                      </div>
                    </div>
                  </main>
                  <Footer />
                  <StorageAlert />
                </div>
              </MaintenanceWrapper>
            {/* </ActivityTrackerProvider> */}
          </ConditionalAuthWrapper>
          <Toaster
            position="top-right"
            expand={true}
            richColors={true}
            closeButton={true}
          />
        </ThemeProvider>
      </body>
    </html>
  );
}
