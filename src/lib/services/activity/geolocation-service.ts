'use client';

import { GeolocationData, DeviceInfo, DeviceType, BrowserType } from '@/types/activity';
import { log } from '../../utils/logger';

export interface ExtendedDeviceInfo extends DeviceInfo {
  // Additional device capabilities
  touchSupport: boolean;
  cookieEnabled: boolean;
  javaEnabled: boolean;
  language: string;
  languages: string[];
  platform: string;
  vendor: string;
  
  // Network information (if available)
  connectionType?: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  
  // Hardware information
  hardwareConcurrency: number;
  maxTouchPoints: number;
  
  // Display information
  colorDepth: number;
  pixelDepth: number;
  devicePixelRatio: number;
}

export interface LocationInfo {
  // Browser geolocation (if permitted)
  browserLocation?: {
    latitude: number;
    longitude: number;
    accuracy: number;
    timestamp: number;
  };
  
  // IP-based geolocation
  ipLocation?: GeolocationData;
  
  // Timezone information
  timezone: string;
  timezoneOffset: number;
}

export class GeolocationService {
  private static ipLocationCache: Map<string, GeolocationData> = new Map();
  private static deviceInfoCache: ExtendedDeviceInfo | null = null;

  /**
   * Get comprehensive device information
   */
  static async getExtendedDeviceInfo(): Promise<ExtendedDeviceInfo> {
    if (this.deviceInfoCache) {
      return this.deviceInfoCache;
    }

    if (typeof window === 'undefined') {
      throw new Error('Device info can only be obtained in browser environment');
    }

    try {
      const userAgent = navigator.userAgent;
      const deviceInfo: ExtendedDeviceInfo = {
        // Basic device info
        type: this.detectDeviceType(userAgent),
        browser: this.detectBrowser(userAgent),
        browserVersion: this.detectBrowserVersion(userAgent),
        os: this.detectOS(userAgent),
        osVersion: this.detectOSVersion(userAgent),
        screenResolution: `${screen.width}x${screen.height}`,
        userAgent,

        // Extended capabilities
        touchSupport: this.detectTouchSupport(),
        cookieEnabled: navigator.cookieEnabled,
        javaEnabled: this.detectJavaEnabled(),
        language: navigator.language,
        languages: Array.from(navigator.languages || [navigator.language]),
        platform: navigator.platform,
        vendor: navigator.vendor || 'unknown',

        // Hardware info
        hardwareConcurrency: navigator.hardwareConcurrency || 1,
        maxTouchPoints: navigator.maxTouchPoints || 0,

        // Display info
        colorDepth: screen.colorDepth || 24,
        pixelDepth: screen.pixelDepth || 24,
        devicePixelRatio: window.devicePixelRatio || 1
      };

      // Add network information if available
      const connection = (navigator as any).connection || 
                        (navigator as any).mozConnection || 
                        (navigator as any).webkitConnection;
      
      if (connection) {
        deviceInfo.connectionType = connection.type;
        deviceInfo.effectiveType = connection.effectiveType;
        deviceInfo.downlink = connection.downlink;
        deviceInfo.rtt = connection.rtt;
      }

      this.deviceInfoCache = deviceInfo;
      return deviceInfo;
    } catch (error) {
      log.error('Failed to get extended device info', 'GEOLOCATION', { error });
      throw error;
    }
  }

  /**
   * Get location information (browser + IP-based)
   */
  static async getLocationInfo(requestBrowserLocation: boolean = false): Promise<LocationInfo> {
    const locationInfo: LocationInfo = {
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timezoneOffset: new Date().getTimezoneOffset()
    };

    // Get browser geolocation if requested and permitted
    if (requestBrowserLocation && typeof window !== 'undefined' && 'geolocation' in navigator) {
      try {
        const position = await this.getBrowserLocation();
        locationInfo.browserLocation = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp
        };
      } catch (error) {
        log.warn('Browser geolocation not available', 'GEOLOCATION', { error });
      }
    }

    // Get IP-based geolocation
    try {
      const ipLocation = await this.getIPLocation();
      locationInfo.ipLocation = ipLocation;
    } catch (error) {
      log.warn('IP geolocation not available', 'GEOLOCATION', { error });
    }

    return locationInfo;
  }

  /**
   * Get IP-based geolocation
   */
  static async getIPLocation(): Promise<GeolocationData | undefined> {
    try {
      // Check cache first
      const cacheKey = 'current_ip';
      if (this.ipLocationCache.has(cacheKey)) {
        return this.ipLocationCache.get(cacheKey);
      }

      // Use a free IP geolocation service
      // Note: In production, you should use a more reliable service with API keys
      const response = await fetch('https://ipapi.co/json/', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.reason || 'IP geolocation failed');
      }

      const geolocation: GeolocationData = {
        country: data.country_name,
        countryCode: data.country_code,
        region: data.region,
        city: data.city,
        latitude: data.latitude,
        longitude: data.longitude,
        timezone: data.timezone,
        isp: data.org
      };

      // Cache the result for 1 hour
      this.ipLocationCache.set(cacheKey, geolocation);
      setTimeout(() => {
        this.ipLocationCache.delete(cacheKey);
      }, 60 * 60 * 1000);

      return geolocation;
    } catch (error) {
      log.error('Failed to get IP geolocation', 'GEOLOCATION', { error });
      return undefined;
    }
  }

  /**
   * Get browser geolocation (requires user permission)
   */
  static async getBrowserLocation(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        resolve,
        reject,
        {
          enableHighAccuracy: false,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }

  /**
   * Detect if device supports touch
   */
  static detectTouchSupport(): boolean {
    return 'ontouchstart' in window || 
           navigator.maxTouchPoints > 0 || 
           (navigator as any).msMaxTouchPoints > 0;
  }

  /**
   * Detect if Java is enabled
   */
  static detectJavaEnabled(): boolean {
    return (navigator as any).javaEnabled ? (navigator as any).javaEnabled() : false;
  }

  /**
   * Detect device type from user agent
   */
  static detectDeviceType(userAgent: string): DeviceType {
    const ua = userAgent.toLowerCase();
    
    if (/tablet|ipad|playbook|silk/i.test(ua)) {
      return DeviceType.TABLET;
    }
    
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(ua)) {
      return DeviceType.MOBILE;
    }
    
    return DeviceType.DESKTOP;
  }

  /**
   * Detect browser from user agent
   */
  static detectBrowser(userAgent: string): BrowserType {
    const ua = userAgent.toLowerCase();
    
    if (ua.includes('edg/')) return BrowserType.EDGE;
    if (ua.includes('chrome/') && !ua.includes('edg/')) return BrowserType.CHROME;
    if (ua.includes('firefox/')) return BrowserType.FIREFOX;
    if (ua.includes('safari/') && !ua.includes('chrome/')) return BrowserType.SAFARI;
    if (ua.includes('opera/') || ua.includes('opr/')) return BrowserType.OPERA;
    
    return BrowserType.OTHER;
  }

  /**
   * Detect browser version
   */
  static detectBrowserVersion(userAgent: string): string {
    const ua = userAgent.toLowerCase();
    let match: RegExpMatchArray | null = null;

    if (ua.includes('edg/')) {
      match = ua.match(/edg\/(\d+)/);
    } else if (ua.includes('chrome/')) {
      match = ua.match(/chrome\/(\d+)/);
    } else if (ua.includes('firefox/')) {
      match = ua.match(/firefox\/(\d+)/);
    } else if (ua.includes('safari/')) {
      match = ua.match(/version\/(\d+)/);
    } else if (ua.includes('opera/') || ua.includes('opr/')) {
      match = ua.match(/(?:opera|opr)\/(\d+)/);
    }

    return match ? match[1] : 'unknown';
  }

  /**
   * Detect operating system
   */
  static detectOS(userAgent: string): string {
    const ua = userAgent.toLowerCase();
    
    if (ua.includes('windows nt 10.0')) return 'Windows 10';
    if (ua.includes('windows nt 6.3')) return 'Windows 8.1';
    if (ua.includes('windows nt 6.2')) return 'Windows 8';
    if (ua.includes('windows nt 6.1')) return 'Windows 7';
    if (ua.includes('windows nt')) return 'Windows';
    if (ua.includes('mac os x')) return 'macOS';
    if (ua.includes('linux')) return 'Linux';
    if (ua.includes('android')) return 'Android';
    if (ua.includes('iphone') || ua.includes('ipad') || ua.includes('ipod')) return 'iOS';
    
    return 'Unknown';
  }

  /**
   * Detect OS version
   */
  static detectOSVersion(userAgent: string): string {
    const ua = userAgent.toLowerCase();
    let match: RegExpMatchArray | null = null;

    if (ua.includes('windows nt')) {
      match = ua.match(/windows nt ([\d.]+)/);
    } else if (ua.includes('mac os x')) {
      match = ua.match(/mac os x ([\d_]+)/);
      if (match) {
        return match[1].replace(/_/g, '.');
      }
    } else if (ua.includes('android')) {
      match = ua.match(/android ([\d.]+)/);
    } else if (ua.includes('iphone') || ua.includes('ipad')) {
      match = ua.match(/os ([\d_]+)/);
      if (match) {
        return match[1].replace(/_/g, '.');
      }
    }

    return match ? match[1] : 'unknown';
  }

  /**
   * Get comprehensive analytics data
   */
  static async getAnalyticsData(requestLocation: boolean = false): Promise<{
    device: ExtendedDeviceInfo;
    location: LocationInfo;
  }> {
    try {
      const [device, location] = await Promise.all([
        this.getExtendedDeviceInfo(),
        this.getLocationInfo(requestLocation)
      ]);

      return { device, location };
    } catch (error) {
      log.error('Failed to get analytics data', 'GEOLOCATION', { error });
      throw error;
    }
  }

  /**
   * Clear caches
   */
  static clearCaches(): void {
    this.ipLocationCache.clear();
    this.deviceInfoCache = null;
  }

  /**
   * Get device fingerprint (for analytics, not tracking)
   */
  static async getDeviceFingerprint(): Promise<string> {
    try {
      const device = await this.getExtendedDeviceInfo();
      
      // Create a hash of device characteristics
      const characteristics = [
        device.userAgent,
        device.screenResolution,
        device.colorDepth.toString(),
        device.timezone,
        device.language,
        device.platform,
        device.hardwareConcurrency.toString(),
        device.devicePixelRatio.toString()
      ].join('|');

      // Simple hash function (for demonstration - use a proper hash in production)
      let hash = 0;
      for (let i = 0; i < characteristics.length; i++) {
        const char = characteristics.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }

      return Math.abs(hash).toString(36);
    } catch (error) {
      log.error('Failed to generate device fingerprint', 'GEOLOCATION', { error });
      return 'unknown';
    }
  }
}
