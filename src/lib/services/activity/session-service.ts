'use client';

import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  setDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';
import { UserSession, PageView, DeviceInfo } from '@/types/activity';
import { log } from '../../utils/logger';

const SESSIONS_COLLECTION = 'user_sessions';
const PAGE_VIEWS_COLLECTION = 'page_views';

export interface SessionAnalytics {
  totalSessions: number;
  activeSessions: number;
  averageDuration: number;
  averagePageViews: number;
  bounceRate: number;
  topEntryPages: Array<{ page: string; count: number }>;
  topExitPages: Array<{ page: string; count: number }>;
  sessionFlow: Array<{
    sessionId: string;
    userId?: string;
    pages: string[];
    duration: number;
    startTime: string;
    endTime?: string;
  }>;
}

export interface VisitorJourney {
  sessionId: string;
  userId?: string;
  startTime: string;
  endTime?: string;
  duration: number;
  pageViews: Array<{
    page: string;
    timestamp: string;
    timeOnPage?: number;
  }>;
  device: DeviceInfo;
  entryPage: string;
  exitPage?: string;
  isActive: boolean;
  totalInteractions: number;
}

export class SessionService {
  private static currentSessionId: string | null = null;
  private static sessionStartTime: number = Date.now();
  private static pageStartTime: number = Date.now();
  private static currentPage: string = '';

  /**
   * Initialize session tracking
   */
  static async initialize(userId?: string): Promise<string> {
    if (typeof window === 'undefined') return '';

    try {
      // Generate new session ID
      this.currentSessionId = this.generateSessionId();
      this.sessionStartTime = Date.now();
      this.currentPage = window.location.pathname;
      this.pageStartTime = Date.now();

      // Create session document
      const session: Omit<UserSession, 'id'> = {
        userId,
        startTime: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        pageViews: 1,
        interactions: 0,
        totalActivities: 0,
        device: await this.getDeviceInfo(),
        pages: [this.currentPage],
        entryPage: this.currentPage,
        isActive: true,
        isAnonymous: !userId
      };

      await setDoc(doc(db, SESSIONS_COLLECTION, this.currentSessionId), session);

      // Set up session monitoring
      this.setupSessionMonitoring();

      log.info('Session initialized', 'SESSION', {
        sessionId: this.currentSessionId,
        userId,
        entryPage: this.currentPage
      });

      return this.currentSessionId;
    } catch (error) {
      log.error('Failed to initialize session', 'SESSION', { error });
      return '';
    }
  }

  /**
   * Update session with user ID after login
   */
  static async updateSessionUser(userId: string): Promise<void> {
    if (!this.currentSessionId) return;

    try {
      await updateDoc(doc(db, SESSIONS_COLLECTION, this.currentSessionId), {
        userId,
        isAnonymous: false,
        lastActivity: new Date().toISOString()
      });

      log.info('Session updated with user ID', 'SESSION', {
        sessionId: this.currentSessionId,
        userId
      });
    } catch (error) {
      log.error('Failed to update session user', 'SESSION', { error });
    }
  }

  /**
   * Track page change
   */
  static async trackPageChange(newPage: string, userId?: string): Promise<void> {
    if (!this.currentSessionId) return;

    try {
      // Calculate time on previous page
      const timeOnPreviousPage = Date.now() - this.pageStartTime;

      // Update session with new page
      const sessionRef = doc(db, SESSIONS_COLLECTION, this.currentSessionId);
      const sessionDoc = await getDoc(sessionRef);

      if (sessionDoc.exists()) {
        const sessionData = sessionDoc.data() as UserSession;
        const updatedPages = [...sessionData.pages];
        
        // Add new page if it's different from the last one
        if (updatedPages[updatedPages.length - 1] !== newPage) {
          updatedPages.push(newPage);
        }

        await updateDoc(sessionRef, {
          pages: updatedPages,
          pageViews: sessionData.pageViews + 1,
          lastActivity: new Date().toISOString(),
          duration: Date.now() - this.sessionStartTime
        });
      }

      // Update current page tracking
      this.currentPage = newPage;
      this.pageStartTime = Date.now();

      log.debug('Page change tracked', 'SESSION', {
        sessionId: this.currentSessionId,
        newPage,
        timeOnPreviousPage
      });
    } catch (error) {
      log.error('Failed to track page change', 'SESSION', { error });
    }
  }

  /**
   * Track user interaction
   */
  static async trackInteraction(): Promise<void> {
    if (!this.currentSessionId) return;

    try {
      const sessionRef = doc(db, SESSIONS_COLLECTION, this.currentSessionId);
      const sessionDoc = await getDoc(sessionRef);

      if (sessionDoc.exists()) {
        const sessionData = sessionDoc.data() as UserSession;
        await updateDoc(sessionRef, {
          interactions: sessionData.interactions + 1,
          totalActivities: sessionData.totalActivities + 1,
          lastActivity: new Date().toISOString(),
          duration: Date.now() - this.sessionStartTime
        });
      }
    } catch (error) {
      log.error('Failed to track interaction', 'SESSION', { error });
    }
  }

  /**
   * End current session
   */
  static async endSession(): Promise<void> {
    if (!this.currentSessionId) return;

    try {
      const sessionRef = doc(db, SESSIONS_COLLECTION, this.currentSessionId);
      await updateDoc(sessionRef, {
        endTime: new Date().toISOString(),
        exitPage: this.currentPage,
        isActive: false,
        duration: Date.now() - this.sessionStartTime
      });

      log.info('Session ended', 'SESSION', {
        sessionId: this.currentSessionId,
        duration: Date.now() - this.sessionStartTime,
        exitPage: this.currentPage
      });

      this.currentSessionId = null;
    } catch (error) {
      log.error('Failed to end session', 'SESSION', { error });
    }
  }

  /**
   * Get current session ID
   */
  static getCurrentSessionId(): string | null {
    return this.currentSessionId;
  }

  /**
   * Get session analytics
   */
  static async getSessionAnalytics(
    timeRange: { start: Date; end: Date },
    userId?: string
  ): Promise<SessionAnalytics> {
    try {
      // Build query
      let q = query(collection(db, SESSIONS_COLLECTION));

      if (userId) {
        q = query(q, where('userId', '==', userId));
      }

      q = query(q,
        where('startTime', '>=', timeRange.start.toISOString()),
        where('startTime', '<=', timeRange.end.toISOString()),
        orderBy('startTime', 'desc')
      );

      const snapshot = await getDocs(q);
      const sessions = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as UserSession[];

      return this.calculateSessionAnalytics(sessions);
    } catch (error) {
      log.error('Failed to get session analytics', 'SESSION', { error });
      throw error;
    }
  }

  /**
   * Get visitor journeys
   */
  static async getVisitorJourneys(
    limit: number = 50,
    userId?: string
  ): Promise<VisitorJourney[]> {
    try {
      let q = query(collection(db, SESSIONS_COLLECTION));

      if (userId) {
        q = query(q, where('userId', '==', userId));
      }

      q = query(q, orderBy('startTime', 'desc'), limit(limit));

      const snapshot = await getDocs(q);
      const sessions = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as UserSession[];

      // Convert sessions to visitor journeys
      const journeys: VisitorJourney[] = [];

      for (const session of sessions) {
        // Get page views for this session
        const pageViewsQuery = query(
          collection(db, PAGE_VIEWS_COLLECTION),
          where('sessionId', '==', session.id),
          orderBy('timestamp', 'asc')
        );

        const pageViewsSnapshot = await getDocs(pageViewsQuery);
        const pageViews = pageViewsSnapshot.docs.map(doc => doc.data()) as PageView[];

        const journey: VisitorJourney = {
          sessionId: session.id,
          userId: session.userId,
          startTime: session.startTime,
          endTime: session.endTime,
          duration: session.duration || 0,
          pageViews: pageViews.map(pv => ({
            page: pv.page,
            timestamp: pv.timestamp,
            timeOnPage: pv.timeOnPage
          })),
          device: session.device,
          entryPage: session.entryPage,
          exitPage: session.exitPage,
          isActive: session.isActive,
          totalInteractions: session.interactions
        };

        journeys.push(journey);
      }

      return journeys;
    } catch (error) {
      log.error('Failed to get visitor journeys', 'SESSION', { error });
      throw error;
    }
  }

  /**
   * Get active sessions count
   */
  static async getActiveSessionsCount(): Promise<number> {
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      
      const q = query(
        collection(db, SESSIONS_COLLECTION),
        where('isActive', '==', true),
        where('lastActivity', '>=', fiveMinutesAgo.toISOString())
      );

      const snapshot = await getDocs(q);
      return snapshot.size;
    } catch (error) {
      log.error('Failed to get active sessions count', 'SESSION', { error });
      return 0;
    }
  }

  /**
   * Subscribe to active sessions changes
   */
  static onActiveSessionsChange(callback: (count: number) => void): () => void {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    
    const q = query(
      collection(db, SESSIONS_COLLECTION),
      where('isActive', '==', true),
      where('lastActivity', '>=', fiveMinutesAgo.toISOString())
    );

    return onSnapshot(q, (snapshot) => {
      callback(snapshot.size);
    });
  }

  /**
   * Private helper methods
   */
  private static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static async getDeviceInfo(): Promise<DeviceInfo> {
    const userAgent = navigator.userAgent;
    
    return {
      type: this.detectDeviceType(userAgent),
      browser: this.detectBrowser(userAgent),
      browserVersion: this.detectBrowserVersion(userAgent),
      os: this.detectOS(userAgent),
      screenResolution: `${screen.width}x${screen.height}`,
      userAgent
    };
  }

  private static detectDeviceType(userAgent: string): DeviceInfo['type'] {
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'tablet';
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'mobile';
    }
    return 'desktop';
  }

  private static detectBrowser(userAgent: string): DeviceInfo['browser'] {
    if (userAgent.includes('Chrome')) return 'chrome';
    if (userAgent.includes('Firefox')) return 'firefox';
    if (userAgent.includes('Safari')) return 'safari';
    if (userAgent.includes('Edge')) return 'edge';
    if (userAgent.includes('Opera')) return 'opera';
    return 'other';
  }

  private static detectBrowserVersion(userAgent: string): string {
    const match = userAgent.match(/(chrome|firefox|safari|edge|opera)\/(\d+)/i);
    return match ? match[2] : 'unknown';
  }

  private static detectOS(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  private static setupSessionMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Update session activity on user interaction
    const updateActivity = () => {
      this.trackInteraction();
    };

    // Listen for various user interactions
    ['click', 'keydown', 'scroll', 'mousemove'].forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true });
    });

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // Page is hidden, user might be switching tabs
        this.trackInteraction();
      } else {
        // Page is visible again
        this.trackInteraction();
      }
    });

    // Handle beforeunload to end session
    window.addEventListener('beforeunload', () => {
      this.endSession();
    });
  }

  private static calculateSessionAnalytics(sessions: UserSession[]): SessionAnalytics {
    const totalSessions = sessions.length;
    const activeSessions = sessions.filter(s => s.isActive).length;
    
    // Calculate average duration (only for completed sessions)
    const completedSessions = sessions.filter(s => s.duration && s.duration > 0);
    const averageDuration = completedSessions.length > 0
      ? completedSessions.reduce((sum, s) => sum + (s.duration || 0), 0) / completedSessions.length
      : 0;

    // Calculate average page views
    const averagePageViews = totalSessions > 0
      ? sessions.reduce((sum, s) => sum + s.pageViews, 0) / totalSessions
      : 0;

    // Calculate bounce rate (sessions with only 1 page view)
    const bouncedSessions = sessions.filter(s => s.pageViews === 1).length;
    const bounceRate = totalSessions > 0 ? (bouncedSessions / totalSessions) * 100 : 0;

    // Calculate top entry and exit pages
    const entryPageCounts: Record<string, number> = {};
    const exitPageCounts: Record<string, number> = {};

    sessions.forEach(session => {
      entryPageCounts[session.entryPage] = (entryPageCounts[session.entryPage] || 0) + 1;
      if (session.exitPage) {
        exitPageCounts[session.exitPage] = (exitPageCounts[session.exitPage] || 0) + 1;
      }
    });

    const topEntryPages = Object.entries(entryPageCounts)
      .map(([page, count]) => ({ page, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const topExitPages = Object.entries(exitPageCounts)
      .map(([page, count]) => ({ page, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Create session flow data
    const sessionFlow = sessions.slice(0, 20).map(session => ({
      sessionId: session.id,
      userId: session.userId,
      pages: session.pages,
      duration: session.duration || 0,
      startTime: session.startTime,
      endTime: session.endTime
    }));

    return {
      totalSessions,
      activeSessions,
      averageDuration,
      averagePageViews,
      bounceRate,
      topEntryPages,
      topExitPages,
      sessionFlow
    };
  }
}
