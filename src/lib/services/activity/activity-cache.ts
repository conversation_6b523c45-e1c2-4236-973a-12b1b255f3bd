'use client';

import { UserActivity, VisitorStats, ActivityAnalytics } from '@/types/activity';
import { log } from '../../utils/logger';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  size: number;
  maxSize: number;
}

export class ActivityCache {
  private static instance: ActivityCache;
  private cache: Map<string, CacheEntry<any>> = new Map();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    size: 0,
    maxSize: 1000
  };
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    // Start cleanup interval
    this.startCleanup();
  }

  static getInstance(): ActivityCache {
    if (!ActivityCache.instance) {
      ActivityCache.instance = new ActivityCache();
    }
    return ActivityCache.instance;
  }

  /**
   * Set cache entry with TTL
   */
  set<T>(key: string, data: T, ttl: number = 300000): void { // Default 5 minutes
    try {
      // Check if we need to evict entries
      if (this.cache.size >= this.stats.maxSize) {
        this.evictOldest();
      }

      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl
      };

      this.cache.set(key, entry);
      this.stats.size = this.cache.size;

      log.debug('Cache entry set', 'ACTIVITY_CACHE', { key, ttl });
    } catch (error) {
      log.error('Failed to set cache entry', 'ACTIVITY_CACHE', { key, error });
    }
  }

  /**
   * Get cache entry
   */
  get<T>(key: string): T | null {
    try {
      const entry = this.cache.get(key) as CacheEntry<T> | undefined;

      if (!entry) {
        this.stats.misses++;
        return null;
      }

      // Check if entry has expired
      if (Date.now() - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        this.stats.size = this.cache.size;
        this.stats.misses++;
        return null;
      }

      this.stats.hits++;
      return entry.data;
    } catch (error) {
      log.error('Failed to get cache entry', 'ACTIVITY_CACHE', { key, error });
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.size = this.cache.size;
      return false;
    }

    return true;
  }

  /**
   * Delete cache entry
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.stats.size = this.cache.size;
    }
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.stats.size = 0;
    this.stats.hits = 0;
    this.stats.misses = 0;
    this.stats.evictions = 0;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get cache hit rate
   */
  getHitRate(): number {
    const total = this.stats.hits + this.stats.misses;
    return total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  /**
   * Evict oldest entries
   */
  private evictOldest(): void {
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

    // Remove oldest 10% of entries
    const toRemove = Math.max(1, Math.floor(entries.length * 0.1));
    for (let i = 0; i < toRemove; i++) {
      this.cache.delete(entries[i][0]);
      this.stats.evictions++;
    }

    this.stats.size = this.cache.size;
  }

  /**
   * Start cleanup interval
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // Cleanup every minute
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      this.stats.size = this.cache.size;
      log.debug('Cache cleanup completed', 'ACTIVITY_CACHE', { cleaned });
    }
  }

  /**
   * Stop cleanup interval
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }
}

// Cache key generators
export class CacheKeys {
  static visitorStats(): string {
    return 'visitor_stats:current';
  }

  static activityAnalytics(filters: any, timeRange: any): string {
    const filterHash = this.hashObject({ filters, timeRange });
    return `activity_analytics:${filterHash}`;
  }

  static sessionAnalytics(timeRange: any, userId?: string): string {
    const filterHash = this.hashObject({ timeRange, userId });
    return `session_analytics:${filterHash}`;
  }

  static recentActivities(limit: number, userId?: string): string {
    return `recent_activities:${limit}:${userId || 'all'}`;
  }

  static userSessions(userId: string): string {
    return `user_sessions:${userId}`;
  }

  static deviceStats(): string {
    return 'device_stats:current';
  }

  static geographicStats(): string {
    return 'geographic_stats:current';
  }

  private static hashObject(obj: any): string {
    const str = JSON.stringify(obj, Object.keys(obj).sort());
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

// Activity data aggregator with caching
export class ActivityAggregator {
  private cache = ActivityCache.getInstance();

  /**
   * Get visitor stats with caching
   */
  async getVisitorStats(forceRefresh: boolean = false): Promise<VisitorStats | null> {
    const cacheKey = CacheKeys.visitorStats();

    if (!forceRefresh) {
      const cached = this.cache.get<VisitorStats>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      // This would typically fetch from your API
      // For now, return null and let the component handle it
      return null;
    } catch (error) {
      log.error('Failed to get visitor stats', 'ACTIVITY_AGGREGATOR', { error });
      return null;
    }
  }

  /**
   * Get activity analytics with caching
   */
  async getActivityAnalytics(
    filters: any,
    timeRange: any,
    forceRefresh: boolean = false
  ): Promise<ActivityAnalytics | null> {
    const cacheKey = CacheKeys.activityAnalytics(filters, timeRange);

    if (!forceRefresh) {
      const cached = this.cache.get<ActivityAnalytics>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      // This would typically fetch from your API
      // For now, return null and let the component handle it
      return null;
    } catch (error) {
      log.error('Failed to get activity analytics', 'ACTIVITY_AGGREGATOR', { error });
      return null;
    }
  }

  /**
   * Invalidate related caches
   */
  invalidateActivityCaches(): void {
    // Clear all activity-related caches
    const cache = ActivityCache.getInstance();
    const stats = cache.getStats();
    
    // Get all keys and remove activity-related ones
    // Note: Map doesn't expose keys directly, so we'd need to track them
    // For now, we'll clear all caches
    cache.clear();
    
    log.info('Activity caches invalidated', 'ACTIVITY_AGGREGATOR', { 
      clearedEntries: stats.size 
    });
  }

  /**
   * Preload common data
   */
  async preloadCommonData(): Promise<void> {
    try {
      // Preload visitor stats
      await this.getVisitorStats(true);

      // Preload recent analytics for common time ranges
      const timeRanges = [
        { start: new Date(Date.now() - 24 * 60 * 60 * 1000), end: new Date() }, // 24h
        { start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), end: new Date() } // 7d
      ];

      for (const timeRange of timeRanges) {
        await this.getActivityAnalytics({}, timeRange, true);
      }

      log.info('Common activity data preloaded', 'ACTIVITY_AGGREGATOR');
    } catch (error) {
      log.error('Failed to preload common data', 'ACTIVITY_AGGREGATOR', { error });
    }
  }
}

// Activity batch processor for performance
export class ActivityBatchProcessor {
  private static instance: ActivityBatchProcessor;
  private batchQueue: UserActivity[] = [];
  private batchTimer: NodeJS.Timeout | null = null;
  private readonly BATCH_SIZE = 50;
  private readonly BATCH_TIMEOUT = 10000; // 10 seconds
  private processing = false;

  private constructor() {}

  static getInstance(): ActivityBatchProcessor {
    if (!ActivityBatchProcessor.instance) {
      ActivityBatchProcessor.instance = new ActivityBatchProcessor();
    }
    return ActivityBatchProcessor.instance;
  }

  /**
   * Add activity to batch queue
   */
  addActivity(activity: UserActivity): void {
    this.batchQueue.push(activity);

    if (this.batchQueue.length >= this.BATCH_SIZE) {
      this.processBatch();
    } else if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => {
        this.processBatch();
      }, this.BATCH_TIMEOUT);
    }
  }

  /**
   * Process current batch
   */
  private async processBatch(): Promise<void> {
    if (this.processing || this.batchQueue.length === 0) return;

    this.processing = true;
    const batch = [...this.batchQueue];
    this.batchQueue = [];

    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    try {
      // Send batch to server
      const response = await fetch('/api/activity/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(batch)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      log.debug('Activity batch processed', 'BATCH_PROCESSOR', {
        batchSize: batch.length
      });
    } catch (error) {
      log.error('Failed to process activity batch', 'BATCH_PROCESSOR', {
        batchSize: batch.length,
        error
      });

      // Re-queue failed activities (with limit to prevent infinite loops)
      if (batch.length < 100) {
        this.batchQueue.unshift(...batch);
      }
    } finally {
      this.processing = false;

      // Process next batch if queue has items
      if (this.batchQueue.length > 0) {
        setTimeout(() => this.processBatch(), 1000);
      }
    }
  }

  /**
   * Force process current batch
   */
  async flush(): Promise<void> {
    if (this.batchQueue.length > 0) {
      await this.processBatch();
    }
  }

  /**
   * Get queue status
   */
  getStatus(): {
    queueSize: number;
    processing: boolean;
  } {
    return {
      queueSize: this.batchQueue.length,
      processing: this.processing
    };
  }
}
