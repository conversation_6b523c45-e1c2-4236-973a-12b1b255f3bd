'use client';

import { 
  collection, 
  addDoc, 
  doc, 
  setDoc, 
  updateDoc, 
  getDoc, 
  getDocs,
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp,
  onSnapshot,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';
import { 
  UserActivity, 
  UserSession, 
  PageView, 
  SystemMetrics,
  VisitorStats,
  ActivityType, 
  DeviceInfo, 
  GeolocationData,
  ActivityMetadata,
  ActivityFilters,
  ActivityAnalytics
} from '@/types/activity';
import { log } from '../../utils/logger';

// Collection names
const ACTIVITIES_COLLECTION = 'user_activities';
const SESSIONS_COLLECTION = 'user_sessions';
const PAGE_VIEWS_COLLECTION = 'page_views';
const SYSTEM_METRICS_COLLECTION = 'system_metrics';
const VISITOR_STATS_COLLECTION = 'visitor_stats';

// Activity batching
interface ActivityBatch {
  activities: Partial<UserActivity>[];
  timestamp: number;
}

export class ActivityService {
  private static batchQueue: ActivityBatch[] = [];
  private static batchTimer: NodeJS.Timeout | null = null;
  private static readonly BATCH_SIZE = 10;
  private static readonly BATCH_TIMEOUT = 5000; // 5 seconds
  private static currentSessionId: string | null = null;
  private static sessionStartTime: number = Date.now();

  /**
   * Initialize activity tracking
   */
  static initialize(): void {
    if (typeof window === 'undefined') return;

    // Generate session ID
    this.currentSessionId = this.generateSessionId();
    this.sessionStartTime = Date.now();

    // Start session
    this.startSession();

    // Set up page visibility tracking
    this.setupPageVisibilityTracking();

    // Set up beforeunload tracking
    this.setupBeforeUnloadTracking();

    log.info('Activity tracking initialized', 'ACTIVITY', {
      sessionId: this.currentSessionId
    });
  }

  /**
   * Track user activity
   */
  static async trackActivity(
    type: ActivityType,
    metadata: ActivityMetadata = {},
    userId?: string
  ): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      const activity: Partial<UserActivity> = {
        userId,
        sessionId: this.currentSessionId || this.generateSessionId(),
        type,
        timestamp: new Date().toISOString(),
        device: await this.getDeviceInfo(),
        metadata,
        ipAddress: await this.getClientIP()
      };

      // Add to batch queue
      this.addToBatch(activity);

      // Update session last activity
      if (this.currentSessionId) {
        await this.updateSessionActivity();
      }

      log.debug('Activity tracked', 'ACTIVITY', { type, metadata });
    } catch (error) {
      log.error('Failed to track activity', 'ACTIVITY', { type, error });
    }
  }

  /**
   * Track page view
   */
  static async trackPageView(
    page: string,
    path: string,
    userId?: string,
    additionalData: Partial<PageView> = {}
  ): Promise<void> {
    try {
      const pageView: Omit<PageView, 'id'> = {
        userId,
        sessionId: this.currentSessionId || this.generateSessionId(),
        page,
        path,
        timestamp: new Date().toISOString(),
        device: await this.getDeviceInfo(),
        referrer: document.referrer,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        ...additionalData
      };

      await addDoc(collection(db, PAGE_VIEWS_COLLECTION), pageView);

      // Also track as activity
      await this.trackActivity(ActivityType.PAGE_VIEW, {
        page,
        path,
        referrer: document.referrer
      }, userId);

      log.debug('Page view tracked', 'ACTIVITY', { page, path });
    } catch (error) {
      log.error('Failed to track page view', 'ACTIVITY', { page, path, error });
    }
  }

  /**
   * Track button click
   */
  static async trackButtonClick(
    elementId: string,
    elementText: string,
    userId?: string,
    customData: Record<string, any> = {}
  ): Promise<void> {
    await this.trackActivity(ActivityType.BUTTON_CLICK, {
      elementId,
      elementText,
      elementType: 'button',
      page: window.location.pathname,
      customData
    }, userId);
  }

  /**
   * Track feature toggle
   */
  static async trackFeatureToggle(
    featureName: string,
    featureState: boolean,
    userId?: string
  ): Promise<void> {
    await this.trackActivity(ActivityType.FEATURE_TOGGLE, {
      featureName,
      featureState,
      page: window.location.pathname
    }, userId);
  }

  /**
   * Track form submission
   */
  static async trackFormSubmit(
    formId: string,
    formData: Record<string, any>,
    userId?: string
  ): Promise<void> {
    // Remove sensitive data
    const sanitizedData = this.sanitizeFormData(formData);
    
    await this.trackActivity(ActivityType.FORM_SUBMIT, {
      formId,
      customData: sanitizedData,
      page: window.location.pathname
    }, userId);
  }

  /**
   * Track user login
   */
  static async trackLogin(userId: string, method: string = 'email'): Promise<void> {
    await this.trackActivity(ActivityType.LOGIN, {
      customData: { method },
      page: window.location.pathname
    }, userId);

    // Update session with user ID
    if (this.currentSessionId) {
      await this.updateSession({ userId, isAnonymous: false });
    }
  }

  /**
   * Track user logout
   */
  static async trackLogout(userId: string): Promise<void> {
    await this.trackActivity(ActivityType.LOGOUT, {
      page: window.location.pathname
    }, userId);

    // End current session
    await this.endSession();
  }

  /**
   * Get real-time visitor stats
   */
  static onVisitorStatsChange(callback: (stats: VisitorStats) => void): () => void {
    const statsRef = doc(db, VISITOR_STATS_COLLECTION, 'current');
    
    return onSnapshot(statsRef, (snapshot) => {
      if (snapshot.exists()) {
        callback(snapshot.data() as VisitorStats);
      }
    });
  }

  /**
   * Get activity analytics
   */
  static async getActivityAnalytics(
    filters: ActivityFilters = {},
    timeRange: { start: Date; end: Date }
  ): Promise<ActivityAnalytics> {
    try {
      // Build query
      let q = query(collection(db, ACTIVITIES_COLLECTION));

      if (filters.userId) {
        q = query(q, where('userId', '==', filters.userId));
      }

      if (filters.type) {
        const types = Array.isArray(filters.type) ? filters.type : [filters.type];
        q = query(q, where('type', 'in', types));
      }

      q = query(q, 
        where('timestamp', '>=', timeRange.start.toISOString()),
        where('timestamp', '<=', timeRange.end.toISOString()),
        orderBy('timestamp', 'desc')
      );

      const snapshot = await getDocs(q);
      const activities = snapshot.docs.map(doc => ({ 
        id: doc.id, 
        ...doc.data() 
      })) as UserActivity[];

      return this.calculateAnalytics(activities);
    } catch (error) {
      log.error('Failed to get activity analytics', 'ACTIVITY', { filters, error });
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static async getDeviceInfo(): Promise<DeviceInfo> {
    const userAgent = navigator.userAgent;
    
    return {
      type: this.detectDeviceType(userAgent),
      browser: this.detectBrowser(userAgent),
      browserVersion: this.detectBrowserVersion(userAgent),
      os: this.detectOS(userAgent),
      screenResolution: `${screen.width}x${screen.height}`,
      userAgent
    };
  }

  private static detectDeviceType(userAgent: string): DeviceInfo['type'] {
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'tablet';
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'mobile';
    }
    return 'desktop';
  }

  private static detectBrowser(userAgent: string): DeviceInfo['browser'] {
    if (userAgent.includes('Chrome')) return 'chrome';
    if (userAgent.includes('Firefox')) return 'firefox';
    if (userAgent.includes('Safari')) return 'safari';
    if (userAgent.includes('Edge')) return 'edge';
    if (userAgent.includes('Opera')) return 'opera';
    return 'other';
  }

  private static detectBrowserVersion(userAgent: string): string {
    const match = userAgent.match(/(chrome|firefox|safari|edge|opera)\/(\d+)/i);
    return match ? match[2] : 'unknown';
  }

  private static detectOS(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  private static async getClientIP(): Promise<string | undefined> {
    try {
      // This would typically be handled by the server
      // For now, we'll leave it undefined and handle it in Firebase Functions
      return undefined;
    } catch (error) {
      return undefined;
    }
  }

  private static addToBatch(activity: Partial<UserActivity>): void {
    // Add to current batch or create new one
    if (this.batchQueue.length === 0) {
      this.batchQueue.push({
        activities: [activity],
        timestamp: Date.now()
      });
    } else {
      const currentBatch = this.batchQueue[this.batchQueue.length - 1];
      currentBatch.activities.push(activity);
    }

    // Process batch if it's full or timeout reached
    if (this.shouldProcessBatch()) {
      this.processBatch();
    } else if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => {
        this.processBatch();
      }, this.BATCH_TIMEOUT);
    }
  }

  private static shouldProcessBatch(): boolean {
    if (this.batchQueue.length === 0) return false;
    
    const currentBatch = this.batchQueue[this.batchQueue.length - 1];
    return currentBatch.activities.length >= this.BATCH_SIZE;
  }

  private static async processBatch(): Promise<void> {
    if (this.batchQueue.length === 0) return;

    try {
      const batch = writeBatch(db);
      const batchesToProcess = [...this.batchQueue];
      
      for (const activityBatch of batchesToProcess) {
        for (const activity of activityBatch.activities) {
          const docRef = doc(collection(db, ACTIVITIES_COLLECTION));
          batch.set(docRef, {
            ...activity,
            batchId: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          });
        }
      }

      await batch.commit();
      
      // Clear processed batches
      this.batchQueue = [];
      
      if (this.batchTimer) {
        clearTimeout(this.batchTimer);
        this.batchTimer = null;
      }

      log.debug('Activity batch processed', 'ACTIVITY', {
        batchCount: batchesToProcess.length,
        activityCount: batchesToProcess.reduce((sum, b) => sum + b.activities.length, 0)
      });
    } catch (error) {
      log.error('Failed to process activity batch', 'ACTIVITY', { error });
    }
  }

  private static async startSession(): Promise<void> {
    if (!this.currentSessionId) return;

    try {
      const session: Omit<UserSession, 'id'> = {
        userId: undefined, // Will be set on login
        startTime: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        pageViews: 0,
        interactions: 0,
        totalActivities: 0,
        device: await this.getDeviceInfo(),
        pages: [window.location.pathname],
        entryPage: window.location.pathname,
        isActive: true,
        isAnonymous: true
      };

      await setDoc(doc(db, SESSIONS_COLLECTION, this.currentSessionId), session);
      
      log.debug('Session started', 'ACTIVITY', { sessionId: this.currentSessionId });
    } catch (error) {
      log.error('Failed to start session', 'ACTIVITY', { error });
    }
  }

  private static async updateSession(updates: Partial<UserSession>): Promise<void> {
    if (!this.currentSessionId) return;

    try {
      const sessionRef = doc(db, SESSIONS_COLLECTION, this.currentSessionId);
      await updateDoc(sessionRef, {
        ...updates,
        lastActivity: new Date().toISOString()
      });
    } catch (error) {
      log.error('Failed to update session', 'ACTIVITY', { error });
    }
  }

  private static async updateSessionActivity(): Promise<void> {
    if (!this.currentSessionId) return;

    try {
      const sessionRef = doc(db, SESSIONS_COLLECTION, this.currentSessionId);
      const sessionDoc = await getDoc(sessionRef);
      
      if (sessionDoc.exists()) {
        const sessionData = sessionDoc.data() as UserSession;
        await updateDoc(sessionRef, {
          lastActivity: new Date().toISOString(),
          totalActivities: (sessionData.totalActivities || 0) + 1,
          duration: Date.now() - this.sessionStartTime
        });
      }
    } catch (error) {
      log.error('Failed to update session activity', 'ACTIVITY', { error });
    }
  }

  private static async endSession(): Promise<void> {
    if (!this.currentSessionId) return;

    try {
      const sessionRef = doc(db, SESSIONS_COLLECTION, this.currentSessionId);
      await updateDoc(sessionRef, {
        endTime: new Date().toISOString(),
        isActive: false,
        duration: Date.now() - this.sessionStartTime,
        exitPage: window.location.pathname
      });

      this.currentSessionId = null;
      
      log.debug('Session ended', 'ACTIVITY');
    } catch (error) {
      log.error('Failed to end session', 'ACTIVITY', { error });
    }
  }

  private static setupPageVisibilityTracking(): void {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.trackActivity(ActivityType.PAGE_LEAVE, {
          page: window.location.pathname
        });
      } else {
        this.trackActivity(ActivityType.PAGE_VIEW, {
          page: window.location.pathname
        });
      }
    });
  }

  private static setupBeforeUnloadTracking(): void {
    window.addEventListener('beforeunload', () => {
      // Process any remaining batches
      if (this.batchQueue.length > 0) {
        // Use sendBeacon for reliable delivery
        const activities = this.batchQueue.flatMap(batch => batch.activities);
        if (navigator.sendBeacon) {
          navigator.sendBeacon('/api/activity/batch', JSON.stringify(activities));
        }
      }
      
      // End session
      this.endSession();
    });
  }

  private static sanitizeFormData(formData: Record<string, any>): Record<string, any> {
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(formData)) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  private static calculateAnalytics(activities: UserActivity[]): ActivityAnalytics {
    // This is a simplified implementation
    // In a real application, you might want to do this calculation on the server
    
    const uniqueUsers = new Set(activities.filter(a => a.userId).map(a => a.userId)).size;
    const uniqueSessions = new Set(activities.map(a => a.sessionId)).size;
    
    return {
      totalActivities: activities.length,
      uniqueUsers,
      uniqueSessions,
      averageSessionDuration: 0, // Would need session data to calculate
      bounceRate: 0, // Would need page view data to calculate
      activityTimeline: [],
      typeBreakdown: [],
      deviceBreakdown: [],
      geographicBreakdown: [],
      pageBreakdown: []
    };
  }
}
