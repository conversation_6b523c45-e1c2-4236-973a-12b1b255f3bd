import { Timestamp } from 'firebase/firestore';

// Activity types enum
export enum ActivityType {
  // User authentication
  LOGIN = 'login',
  LOGOUT = 'logout',
  SIGNUP = 'signup',
  
  // Page navigation
  PAGE_VIEW = 'page_view',
  PAGE_LEAVE = 'page_leave',
  
  // User interactions
  BUTTON_CLICK = 'button_click',
  FORM_SUBMIT = 'form_submit',
  FEATURE_TOGGLE = 'feature_toggle',
  
  // Checklist operations
  CHECKLIST_CREATE = 'checklist_create',
  CHECKLIST_UPDATE = 'checklist_update',
  CHECKLIST_DELETE = 'checklist_delete',
  CHECKLIST_EXPORT = 'checklist_export',
  
  // Equipment operations
  EQUIPMENT_CREATE = 'equipment_create',
  EQUIPMENT_UPDATE = 'equipment_update',
  EQUIPMENT_DELETE = 'equipment_delete',
  
  // Admin operations
  USER_CREATE = 'user_create',
  USER_UPDATE = 'user_update',
  USER_DELETE = 'user_delete',
  ROLE_CHANGE = 'role_change',
  
  // System events
  ERROR = 'error',
  PERFORMANCE = 'performance',
  SYSTEM_EVENT = 'system_event'
}

// Device types
export enum DeviceType {
  DESKTOP = 'desktop',
  MOBILE = 'mobile',
  TABLET = 'tablet',
  UNKNOWN = 'unknown'
}

// Browser types
export enum BrowserType {
  CHROME = 'chrome',
  FIREFOX = 'firefox',
  SAFARI = 'safari',
  EDGE = 'edge',
  OPERA = 'opera',
  OTHER = 'other'
}

// Geolocation data
export interface GeolocationData {
  country?: string;
  countryCode?: string;
  region?: string;
  city?: string;
  latitude?: number;
  longitude?: number;
  timezone?: string;
  isp?: string;
}

// Device information
export interface DeviceInfo {
  type: DeviceType;
  browser: BrowserType;
  browserVersion?: string;
  os?: string;
  osVersion?: string;
  screenResolution?: string;
  userAgent: string;
}

// Session information
export interface SessionInfo {
  sessionId: string;
  startTime: string;
  lastActivity: string;
  duration?: number; // in milliseconds
  pageCount: number;
  isActive: boolean;
}

// Activity metadata
export interface ActivityMetadata {
  // Page context
  page?: string;
  path?: string;
  referrer?: string;
  
  // Element context (for clicks)
  elementId?: string;
  elementClass?: string;
  elementText?: string;
  elementType?: string;
  
  // Form context
  formId?: string;
  fieldName?: string;
  
  // Feature context
  featureName?: string;
  featureState?: boolean;
  
  // Performance context
  loadTime?: number;
  renderTime?: number;
  
  // Error context
  errorMessage?: string;
  errorStack?: string;
  
  // Custom data
  customData?: Record<string, any>;
}

// Main activity document
export interface UserActivity {
  id: string;
  userId?: string; // null for anonymous users
  sessionId: string;
  type: ActivityType;
  timestamp: Timestamp | string;
  
  // Location and device
  ipAddress?: string;
  geolocation?: GeolocationData;
  device: DeviceInfo;
  
  // Activity details
  metadata: ActivityMetadata;
  
  // Performance tracking
  processingTime?: number; // time to process on server
  batchId?: string; // for batched activities
}

// Session document
export interface UserSession {
  id: string; // sessionId
  userId?: string; // null for anonymous users
  startTime: Timestamp | string;
  endTime?: Timestamp | string;
  lastActivity: Timestamp | string;
  duration?: number; // in milliseconds
  
  // Session stats
  pageViews: number;
  interactions: number;
  totalActivities: number;
  
  // Location and device (from first activity)
  ipAddress?: string;
  geolocation?: GeolocationData;
  device: DeviceInfo;
  
  // Page flow
  pages: string[]; // ordered list of pages visited
  entryPage: string;
  exitPage?: string;
  
  // Session state
  isActive: boolean;
  isAnonymous: boolean;
}

// Page view tracking
export interface PageView {
  id: string;
  userId?: string;
  sessionId: string;
  page: string;
  path: string;
  timestamp: Timestamp | string;
  
  // Page metrics
  loadTime?: number;
  timeOnPage?: number; // calculated when leaving page
  scrollDepth?: number; // percentage
  
  // Navigation context
  referrer?: string;
  entryMethod?: 'direct' | 'referral' | 'search' | 'social' | 'internal';
  
  // Device context
  device: DeviceInfo;
  viewport?: {
    width: number;
    height: number;
  };
}

// System metrics for monitoring
export interface SystemMetrics {
  id: string;
  timestamp: Timestamp | string;
  type: 'performance' | 'usage' | 'error' | 'system';
  
  // Metrics data
  metrics: {
    // Performance metrics
    responseTime?: number;
    memoryUsage?: number;
    cpuUsage?: number;
    
    // Usage metrics
    activeUsers?: number;
    activeSessions?: number;
    totalPageViews?: number;
    totalActivities?: number;
    
    // Error metrics
    errorCount?: number;
    errorRate?: number;
    
    // Custom metrics
    customMetrics?: Record<string, number>;
  };
  
  // Aggregation period
  period: 'minute' | 'hour' | 'day';
  periodStart: Timestamp | string;
  periodEnd: Timestamp | string;
}

// Real-time visitor tracking
export interface VisitorStats {
  id: string;
  timestamp: Timestamp | string;
  
  // Current stats
  liveVisitors: number;
  totalVisitors: number;
  totalPageViews: number;
  totalSessions: number;
  
  // Breakdown by type
  authenticatedUsers: number;
  anonymousUsers: number;
  
  // Device breakdown
  deviceBreakdown: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  
  // Geographic breakdown
  topCountries: Array<{
    country: string;
    count: number;
  }>;
  
  // Page breakdown
  topPages: Array<{
    page: string;
    views: number;
  }>;
}

// Activity tracking configuration
export interface ActivityConfig {
  enabled: boolean;
  trackAnonymous: boolean;
  trackPageViews: boolean;
  trackClicks: boolean;
  trackFormSubmissions: boolean;
  trackPerformance: boolean;
  trackErrors: boolean;
  
  // Sampling rates (0-1)
  samplingRate: number;
  performanceSamplingRate: number;
  
  // Batch settings
  batchSize: number;
  batchTimeout: number; // milliseconds
  
  // Privacy settings
  excludePersonalData: boolean;
  hashIpAddresses: boolean;
  
  // Retention settings
  retentionDays: number;
}

// Activity query filters
export interface ActivityFilters {
  userId?: string;
  sessionId?: string;
  type?: ActivityType | ActivityType[];
  page?: string;
  dateFrom?: string;
  dateTo?: string;
  country?: string;
  device?: DeviceType;
  browser?: BrowserType;
  isAnonymous?: boolean;
}

// Activity analytics data
export interface ActivityAnalytics {
  totalActivities: number;
  uniqueUsers: number;
  uniqueSessions: number;
  averageSessionDuration: number;
  bounceRate: number;
  
  // Time series data
  activityTimeline: Array<{
    timestamp: string;
    count: number;
    type?: ActivityType;
  }>;
  
  // Breakdown data
  typeBreakdown: Array<{
    type: ActivityType;
    count: number;
    percentage: number;
  }>;
  
  deviceBreakdown: Array<{
    device: DeviceType;
    count: number;
    percentage: number;
  }>;
  
  geographicBreakdown: Array<{
    country: string;
    count: number;
    percentage: number;
  }>;
  
  pageBreakdown: Array<{
    page: string;
    views: number;
    uniqueUsers: number;
    averageTime: number;
  }>;
}
