import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { Request, Response } from 'express';

// Activity types and interfaces (duplicated for functions)
interface GeolocationData {
  country?: string;
  countryCode?: string;
  region?: string;
  city?: string;
  latitude?: number;
  longitude?: number;
  timezone?: string;
  isp?: string;
}

interface ActivityBatchItem {
  userId?: string;
  sessionId: string;
  type: string;
  timestamp: string;
  device: any;
  metadata: any;
  ipAddress?: string;
}

interface VisitorStats {
  liveVisitors: number;
  totalVisitors: number;
  totalPageViews: number;
  totalSessions: number;
  authenticatedUsers: number;
  anonymousUsers: number;
  deviceBreakdown: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  topCountries: Array<{
    country: string;
    count: number;
  }>;
  topPages: Array<{
    page: string;
    views: number;
  }>;
}

// Collection names
const ACTIVITIES_COLLECTION = 'user_activities';
const SESSIONS_COLLECTION = 'user_sessions';
const PAGE_VIEWS_COLLECTION = 'page_views';
const VISITOR_STATS_COLLECTION = 'visitor_stats';

/**
 * Process activity batch from client
 */
export const processActivityBatch = functions.https.onRequest(async (req: Request, res: Response) => {
  // Enable CORS
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).send();
    return;
  }

  if (req.method !== 'POST') {
    res.status(405).send('Method not allowed');
    return;
  }

  try {
    const activities: ActivityBatchItem[] = req.body;
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    
    if (!Array.isArray(activities) || activities.length === 0) {
      res.status(400).send('Invalid activity data');
      return;
    }

    const db = admin.firestore();
    const batch = db.batch();
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Get geolocation for IP
    const geolocation = await getGeolocation(clientIP);

    // Process each activity
    for (const activity of activities) {
      const docRef = db.collection(ACTIVITIES_COLLECTION).doc();
      
      const enrichedActivity = {
        ...activity,
        id: docRef.id,
        ipAddress: clientIP,
        geolocation,
        batchId,
        processingTime: Date.now(),
        timestamp: admin.firestore.Timestamp.fromDate(new Date(activity.timestamp))
      };

      batch.set(docRef, enrichedActivity);
    }

    // Commit batch
    await batch.commit();

    // Update visitor stats
    await updateVisitorStats(activities, geolocation);

    console.log(`Processed activity batch: ${activities.length} activities`);
    res.status(200).json({ 
      success: true, 
      processed: activities.length,
      batchId 
    });

  } catch (error) {
    console.error('Error processing activity batch:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
});

/**
 * Get real-time visitor statistics
 */
export const getVisitorStats = functions.https.onRequest(async (req: Request, res: Response) => {
  // Enable CORS
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).send();
    return;
  }

  try {
    const db = admin.firestore();
    const statsDoc = await db.collection(VISITOR_STATS_COLLECTION).doc('current').get();
    
    if (statsDoc.exists) {
      res.status(200).json(statsDoc.data());
    } else {
      // Return default stats
      const defaultStats: VisitorStats = {
        liveVisitors: 0,
        totalVisitors: 0,
        totalPageViews: 0,
        totalSessions: 0,
        authenticatedUsers: 0,
        anonymousUsers: 0,
        deviceBreakdown: { desktop: 0, mobile: 0, tablet: 0 },
        topCountries: [],
        topPages: []
      };
      res.status(200).json(defaultStats);
    }
  } catch (error) {
    console.error('Error getting visitor stats:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Scheduled function to update visitor statistics
 */
export const updateVisitorStatsScheduled = functions.pubsub
  .schedule('every 1 minutes')
  .onRun(async (context) => {
    try {
      const db = admin.firestore();
      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

      // Get active sessions (last activity within 5 minutes)
      const activeSessionsQuery = await db.collection(SESSIONS_COLLECTION)
        .where('lastActivity', '>=', admin.firestore.Timestamp.fromDate(fiveMinutesAgo))
        .where('isActive', '==', true)
        .get();

      const activeSessions = activeSessionsQuery.docs.map(doc => doc.data());

      // Calculate stats
      const stats: VisitorStats = {
        liveVisitors: activeSessions.length,
        totalVisitors: activeSessions.length, // This would be calculated differently in production
        totalPageViews: 0, // Would need to aggregate page views
        totalSessions: activeSessions.length,
        authenticatedUsers: activeSessions.filter(s => !s.isAnonymous).length,
        anonymousUsers: activeSessions.filter(s => s.isAnonymous).length,
        deviceBreakdown: {
          desktop: activeSessions.filter(s => s.device?.type === 'desktop').length,
          mobile: activeSessions.filter(s => s.device?.type === 'mobile').length,
          tablet: activeSessions.filter(s => s.device?.type === 'tablet').length
        },
        topCountries: calculateTopCountries(activeSessions),
        topPages: [] // Would need to aggregate page views
      };

      // Update stats document
      await db.collection(VISITOR_STATS_COLLECTION).doc('current').set({
        ...stats,
        timestamp: admin.firestore.Timestamp.now()
      });

      console.log('Visitor stats updated:', stats);
      return null;
    } catch (error) {
      console.error('Error updating visitor stats:', error);
      return null;
    }
  });

/**
 * Clean up old activity data
 */
export const cleanupOldActivities = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async (context) => {
    try {
      const db = admin.firestore();
      const retentionDays = 90; // Keep 90 days of data
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      // Clean up activities
      const activitiesQuery = await db.collection(ACTIVITIES_COLLECTION)
        .where('timestamp', '<', admin.firestore.Timestamp.fromDate(cutoffDate))
        .limit(500) // Process in batches
        .get();

      if (!activitiesQuery.empty) {
        const batch = db.batch();
        activitiesQuery.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        console.log(`Deleted ${activitiesQuery.docs.length} old activities`);
      }

      // Clean up old sessions
      const sessionsQuery = await db.collection(SESSIONS_COLLECTION)
        .where('startTime', '<', admin.firestore.Timestamp.fromDate(cutoffDate))
        .limit(500)
        .get();

      if (!sessionsQuery.empty) {
        const batch = db.batch();
        sessionsQuery.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        console.log(`Deleted ${sessionsQuery.docs.length} old sessions`);
      }

      return null;
    } catch (error) {
      console.error('Error cleaning up old activities:', error);
      return null;
    }
  });

/**
 * Helper function to get geolocation from IP
 */
async function getGeolocation(ip: string): Promise<GeolocationData | undefined> {
  try {
    // In production, you would use a service like MaxMind, IPinfo, or similar
    // For now, we'll return undefined and handle it gracefully
    
    // Example with a free service (be careful about rate limits):
    // const response = await fetch(`http://ip-api.com/json/${ip}`);
    // const data = await response.json();
    // 
    // if (data.status === 'success') {
    //   return {
    //     country: data.country,
    //     countryCode: data.countryCode,
    //     region: data.regionName,
    //     city: data.city,
    //     latitude: data.lat,
    //     longitude: data.lon,
    //     timezone: data.timezone,
    //     isp: data.isp
    //   };
    // }

    return undefined;
  } catch (error) {
    console.error('Error getting geolocation:', error);
    return undefined;
  }
}

/**
 * Helper function to update visitor stats
 */
async function updateVisitorStats(activities: ActivityBatchItem[], geolocation?: GeolocationData): Promise<void> {
  try {
    const db = admin.firestore();
    const statsRef = db.collection(VISITOR_STATS_COLLECTION).doc('current');
    
    // This is a simplified update - in production you'd want more sophisticated aggregation
    await db.runTransaction(async (transaction) => {
      const statsDoc = await transaction.get(statsRef);
      const currentStats = statsDoc.exists ? statsDoc.data() as VisitorStats : {
        liveVisitors: 0,
        totalVisitors: 0,
        totalPageViews: 0,
        totalSessions: 0,
        authenticatedUsers: 0,
        anonymousUsers: 0,
        deviceBreakdown: { desktop: 0, mobile: 0, tablet: 0 },
        topCountries: [],
        topPages: []
      };

      // Update page view count
      const pageViews = activities.filter(a => a.type === 'page_view').length;
      currentStats.totalPageViews += pageViews;

      transaction.set(statsRef, {
        ...currentStats,
        timestamp: admin.firestore.Timestamp.now()
      });
    });
  } catch (error) {
    console.error('Error updating visitor stats:', error);
  }
}

/**
 * Helper function to calculate top countries
 */
function calculateTopCountries(sessions: any[]): Array<{ country: string; count: number }> {
  const countryCount: Record<string, number> = {};
  
  sessions.forEach(session => {
    if (session.geolocation?.country) {
      countryCount[session.geolocation.country] = (countryCount[session.geolocation.country] || 0) + 1;
    }
  });

  return Object.entries(countryCount)
    .map(([country, count]) => ({ country, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}
