# Activity Monitoring System

## Overview

The Activity Monitoring System is a comprehensive full-stack solution for tracking and analyzing user interactions in the Auburn Engineering admin panel. It provides real-time monitoring, analytics, and reporting capabilities for all user activities across the application.

## Features

### Core Tracking Capabilities
- **User Authentication Events**: Login, logout, signup tracking
- **Page Navigation**: Page views, page leave events, navigation flow
- **User Interactions**: Button clicks, form submissions, feature toggles
- **Business Operations**: Checklist operations, equipment management, admin actions
- **System Events**: Errors, performance metrics, system status

### Real-time Monitoring
- **Live Visitor Count**: Real-time active user tracking
- **Activity Feed**: Live stream of user interactions
- **Session Monitoring**: Active sessions with duration tracking
- **Geographic Distribution**: IP-based geolocation tracking

### Analytics & Reporting
- **Activity Analytics**: Comprehensive activity breakdowns and trends
- **Session Analytics**: Session duration, bounce rate, page flow analysis
- **Device Analytics**: Device type, browser, OS distribution
- **Performance Metrics**: Page load times, resource performance, error rates

### Privacy & Compliance
- **Data Anonymization**: Optional IP hashing and personal data exclusion
- **Sampling Rates**: Configurable sampling for performance optimization
- **Data Retention**: Automatic cleanup of old activity data
- **GDPR Compliance**: Privacy-focused design with opt-out capabilities

## Architecture

### Database Schema

#### Collections
1. **user_activities**: Individual user activity records
2. **user_sessions**: Session tracking with duration and flow
3. **page_views**: Detailed page view analytics
4. **system_metrics**: Aggregated system performance metrics
5. **visitor_stats**: Real-time visitor statistics

#### Key Data Models
```typescript
interface UserActivity {
  id: string;
  userId?: string;
  sessionId: string;
  type: ActivityType;
  timestamp: string;
  ipAddress?: string;
  geolocation?: GeolocationData;
  device: DeviceInfo;
  metadata: ActivityMetadata;
}

interface UserSession {
  id: string;
  userId?: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  pageViews: number;
  interactions: number;
  pages: string[];
  entryPage: string;
  exitPage?: string;
  isActive: boolean;
}
```

### Backend Services

#### Firebase Functions
- **processActivityBatch**: Processes batched activity data from clients
- **getVisitorStats**: Returns real-time visitor statistics
- **updateVisitorStatsScheduled**: Scheduled function to update visitor metrics
- **cleanupOldActivities**: Automated data retention management

#### Core Services
- **ActivityService**: Client-side activity tracking and batching
- **SessionService**: Session management and analytics
- **GeolocationService**: IP geolocation and device detection
- **ActivityCache**: Performance optimization through intelligent caching

### Frontend Components

#### Admin Dashboard
- **ActivityDashboard**: Real-time monitoring dashboard
- **ActivityAnalytics**: Comprehensive analytics and reporting
- **VisitorStats**: Live visitor tracking and metrics

#### Tracking Components
- **ActivityTrackerProvider**: Global activity tracking context
- **useActivityTracker**: Hook for manual activity tracking
- **useUserActionTracker**: Business-specific action tracking

## Implementation Guide

### 1. Database Setup

Create Firestore collections with proper indexing:

```javascript
// Firestore indexes needed
db.collection('user_activities').createIndex({
  userId: 1,
  timestamp: -1
});

db.collection('user_activities').createIndex({
  type: 1,
  timestamp: -1
});

db.collection('user_sessions').createIndex({
  isActive: 1,
  lastActivity: -1
});
```

### 2. Firebase Functions Deployment

Deploy the activity tracking functions:

```bash
# Deploy all functions
firebase deploy --only functions

# Deploy specific activity functions
firebase deploy --only functions:processActivityBatch,getVisitorStats
```

### 3. Client Integration

Add the activity tracker to your app layout:

```tsx
import { ActivityTrackerProvider } from '@/components/activity/activity-tracker-provider';

export default function RootLayout({ children }) {
  return (
    <ActivityTrackerProvider>
      {children}
    </ActivityTrackerProvider>
  );
}
```

### 4. Admin Panel Integration

Add activity monitoring to the admin panel:

```tsx
import { ActivityDashboard } from '@/components/admin/activity-dashboard';
import { ActivityAnalyticsComponent } from '@/components/admin/activity-analytics';

// In your admin panel tabs
<TabsContent value="activity">
  <ActivityDashboard />
  <ActivityAnalyticsComponent />
</TabsContent>
```

## Configuration

### Activity Tracking Configuration

```typescript
const config: ActivityConfig = {
  enabled: true,
  trackAnonymous: true,
  trackPageViews: true,
  trackClicks: true,
  trackFormSubmissions: true,
  trackPerformance: true,
  trackErrors: true,
  samplingRate: 1.0, // 100% sampling
  performanceSamplingRate: 0.1, // 10% performance sampling
  batchSize: 10,
  batchTimeout: 5000,
  excludePersonalData: true,
  retentionDays: 90
};
```

### Environment Variables

```env
# Firebase configuration (existing)
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key

# Activity tracking configuration
ACTIVITY_TRACKING_ENABLED=true
ACTIVITY_SAMPLING_RATE=1.0
ACTIVITY_RETENTION_DAYS=90
```

## Usage Examples

### Manual Activity Tracking

```tsx
import { useActivityTracker } from '@/hooks/useActivityTracker';

function MyComponent() {
  const { trackButtonClick, trackFeatureToggle } = useActivityTracker();

  const handleButtonClick = async () => {
    await trackButtonClick('save-button', 'Save Changes');
    // Your button logic here
  };

  const handleFeatureToggle = async (enabled: boolean) => {
    await trackFeatureToggle('dark-mode', enabled);
    // Your toggle logic here
  };

  return (
    <div>
      <button onClick={handleButtonClick}>Save Changes</button>
      <toggle onChange={handleFeatureToggle} />
    </div>
  );
}
```

### Business Event Tracking

```tsx
import { useManualActivityTracker } from '@/components/activity/activity-tracker-provider';

function ChecklistComponent() {
  const { trackBusinessEvent } = useManualActivityTracker();

  const handleChecklistCreate = async (checklistId: string) => {
    await trackBusinessEvent(
      'checklist_action',
      'create',
      checklistId,
      { category: 'maintenance' }
    );
  };

  return <div>...</div>;
}
```

## Performance Considerations

### Batching
- Activities are batched client-side before sending to server
- Configurable batch size and timeout
- Automatic retry for failed batches

### Caching
- Intelligent caching of analytics data
- Configurable TTL for different data types
- Cache invalidation on data updates

### Sampling
- Configurable sampling rates for different activity types
- Performance metrics use lower sampling rates
- Error tracking uses full sampling

## Privacy & Security

### Data Protection
- IP addresses can be hashed for privacy
- Personal data exclusion options
- Configurable data retention periods

### Access Control
- Activity data only accessible to admin users
- Role-based access to different analytics levels
- Audit trail for admin actions

## Monitoring & Maintenance

### Health Checks
- Monitor Firebase Functions performance
- Track cache hit rates and performance
- Alert on high error rates or failed batches

### Data Cleanup
- Automated cleanup of old activity data
- Configurable retention policies
- Manual cleanup tools for admins

### Performance Monitoring
- Track system performance impact
- Monitor database query performance
- Optimize based on usage patterns

## Troubleshooting

### Common Issues

1. **High Firebase Costs**
   - Reduce sampling rates
   - Increase batch sizes
   - Optimize Firestore queries

2. **Slow Dashboard Loading**
   - Enable caching
   - Reduce time ranges
   - Optimize chart data processing

3. **Missing Activity Data**
   - Check Firebase Functions logs
   - Verify client-side tracking setup
   - Check network connectivity

### Debug Mode

Enable debug logging:

```typescript
// In development
localStorage.setItem('activity-debug', 'true');
```

## Future Enhancements

### Planned Features
- Real-time alerts for suspicious activity
- Advanced user behavior analysis
- Integration with external analytics tools
- Machine learning-based insights
- Custom dashboard widgets

### API Extensions
- REST API for external integrations
- Webhook support for real-time notifications
- Data export APIs for compliance
- Custom metric definitions

## Support

For issues or questions about the Activity Monitoring System:

1. Check the troubleshooting section above
2. Review Firebase Functions logs
3. Check browser console for client-side errors
4. Contact the development team for assistance

## License

This Activity Monitoring System is part of the Auburn Engineering application and follows the same licensing terms.
